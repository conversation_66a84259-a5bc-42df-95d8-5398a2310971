import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { bookAPI } from '@/lib/bookApi';

interface ChapterSummaryProps {
  visible: boolean;
  onClose: () => void;
  bookId: string;
  chapterId: string;
  chapterTitle: string;
}

interface SummaryData {
  summary: string;
  chapter_title: string;
  word_count: number;
  summary_word_count: number;
  language: string;
}

const ChapterSummary: React.FC<ChapterSummaryProps> = ({
  visible,
  onClose,
  bookId,
  chapterId,
  chapterTitle
}) => {
  const [loading, setLoading] = useState(false);
  const [summaryData, setSummaryData] = useState<SummaryData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [language, setLanguage] = useState('zh-CN');

  const generateSummary = async () => {
    setLoading(true);
    setError(null);
    setSummaryData(null);

    try {
      console.log('🔍 开始调用章节总结API...');
      console.log('📝 请求参数:', { bookId, chapterId, language });
      
      const data = await bookAPI.generateChapterSummary(bookId, chapterId, language);
      
      console.log('📡 收到响应:', data);
      console.log('🔎 响应类型:', typeof data);
      console.log('📄 响应键值:', Object.keys(data || {}));

      if (data?.success) {
        console.log('✅ 总结生成成功!');
        console.log('📊 总结数据:', {
          hasTitle: !!data.chapter_title,
          hasSummary: !!data.summary,
          wordCount: data.word_count,
          summaryWordCount: data.summary_word_count,
          language: data.language
        });
        setSummaryData(data);
      } else {
        console.error('❌ 总结生成失败:', data);
        setError(data?.error || '生成总结失败');
      }
    } catch (err: any) {
      console.error('🚨 Summary generation error:', err);
      console.error('🔍 Error details:', {
        message: err.message,
        response: err.response,
        status: err.response?.status,
        data: err.response?.data
      });
      
      if (err.response?.status === 401) {
        setError('请重新登录后再试');
      } else if (err.response?.data?.error) {
        setError(err.response.data.error);
      } else if (err.message) {
        setError(`错误: ${err.message}`);
      } else {
        setError('网络错误，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCopy = () => {
    if (summaryData?.summary) {
      navigator.clipboard.writeText(summaryData.summary);
      // 可以添加一个toast提示
    }
  };

  if (!visible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">AI章节总结</h2>
              <p className="text-sm text-gray-600">{chapterTitle}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-100 rounded-lg"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 控制栏 */}
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-gray-700">总结语言：</label>
                <select
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                  disabled={loading}
                  className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="zh-CN">中文</option>
                  <option value="en">English</option>
                </select>
              </div>
              
              <button
                onClick={generateSummary}
                disabled={loading}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 transition-colors"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>生成中...</span>
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                    <span>生成总结</span>
                  </>
                )}
              </button>
            </div>

            {summaryData && (
              <div className="flex items-center space-x-4">
                <button
                  onClick={handleCopy}
                  className="flex items-center space-x-2 px-3 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
                  title="复制总结"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  <span>复制</span>
                </button>
                
                <div className="text-sm text-gray-500">
                  总结：{summaryData.summary_word_count}字 | 原文：{summaryData.word_count}字
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {loading && (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
              <p className="text-gray-600">AI正在分析章节内容，生成总结中...</p>
              <p className="text-sm text-gray-500 mt-2">这可能需要几十秒时间，请耐心等待</p>
            </div>
          )}

          {error && (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="p-4 bg-red-50 rounded-lg border border-red-200 mb-4">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <span className="text-red-700 font-medium">生成失败</span>
                </div>
                <p className="text-red-600 mt-1">{error}</p>
              </div>
              <button
                onClick={generateSummary}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                重新生成
              </button>
            </div>
          )}

          {summaryData && !loading && !error && (
            <div className="prose prose-lg max-w-none markdown-content">
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                  h1: ({ children }) => (
                    <h1 className="text-2xl font-bold text-gray-900 mb-4 pb-2 border-b border-gray-200">
                      {children}
                    </h1>
                  ),
                  h2: ({ children }) => (
                    <h2 className="text-xl font-semibold text-gray-800 mb-3 mt-6">
                      {children}
                    </h2>
                  ),
                  h3: ({ children }) => (
                    <h3 className="text-lg font-medium text-gray-700 mb-2 mt-4">
                      {children}
                    </h3>
                  ),
                  ul: ({ children }) => (
                    <ul className="list-disc list-inside space-y-1 mb-4 text-gray-700">
                      {children}
                    </ul>
                  ),
                  li: ({ children }) => (
                    <li className="leading-relaxed">{children}</li>
                  ),
                  p: ({ children }) => (
                    <p className="mb-4 leading-relaxed text-gray-700">{children}</p>
                  ),
                  strong: ({ children }) => (
                    <strong className="font-semibold text-gray-900">{children}</strong>
                  ),
                  code: ({ children }) => (
                    <code className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm font-mono">
                      {children}
                    </code>
                  ),
                  hr: () => (
                    <hr className="my-6 border-gray-300" />
                  ),
                  em: ({ children }) => (
                    <em className="text-gray-600 italic">{children}</em>
                  )
                }}
              >
                {summaryData.summary}
              </ReactMarkdown>
            </div>
          )}

          {!summaryData && !loading && !error && (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="p-4 bg-blue-50 rounded-lg mb-4">
                <svg className="w-12 h-12 text-blue-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">准备生成章节总结</h3>
              <p className="text-gray-600 text-center mb-4">
                点击"生成总结"按钮，AI将为您分析章节内容<br />
                并生成结构化的总结报告
              </p>
              <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
                <p>💡 总结将包含：核心要点、详细概述、关键人物、重要细节等</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChapterSummary;
