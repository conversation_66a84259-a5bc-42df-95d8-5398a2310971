#!/usr/bin/env python3
"""
翻译设置API测试脚本
测试翻译设置的获取和更新功能
"""

import os
import sys
import django
import requests
import json
from pathlib import Path

# 设置Django环境
sys.path.insert(0, str(Path(__file__).parent.parent))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ebook_platform.settings')
django.setup()

from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken

User = get_user_model()

def get_jwt_token(username='testuser', email='<EMAIL>'):
    """获取JWT token"""
    try:
        user, created = User.objects.get_or_create(
            username=username,
            defaults={'email': email}
        )
        if created:
            user.set_password('testpassword123')
            user.save()
            print(f"创建测试用户: {username}")
        else:
            print(f"使用现有用户: {username}")
        
        refresh = RefreshToken.for_user(user)
        return str(refresh.access_token)
    except Exception as e:
        print(f"获取JWT token失败: {e}")
        return None

def test_translation_settings_api():
    """测试翻译设置API"""
    base_url = 'http://127.0.0.1:8000'
    
    # 获取JWT token
    token = get_jwt_token()
    if not token:
        print("无法获取认证token，跳过API测试")
        return
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    print("=" * 60)
    print("测试翻译设置API")
    print("=" * 60)
    
    # 1. 测试获取翻译设置
    print("\n1. 测试获取翻译设置...")
    try:
        response = requests.get(f'{base_url}/api/books/translation/settings/', headers=headers)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("响应数据:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 检查是否包含沉浸式翻译字段
            immersive_fields = ['immersive_enabled', 'immersive_auto_translate', 'immersive_split_ratio']
            missing_fields = []
            
            for field in immersive_fields:
                if field not in data:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"⚠️  缺少沉浸式翻译字段: {missing_fields}")
            else:
                print("✅ 所有沉浸式翻译字段都存在")
        else:
            print(f"❌ 获取翻译设置失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 2. 测试更新翻译设置
    print("\n2. 测试更新翻译设置...")
    try:
        update_data = {
            'immersive_enabled': True,
            'immersive_auto_translate': True,
            'immersive_split_ratio': 0.6,
            'default_target_language': 'en'
        }
        
        response = requests.put(
            f'{base_url}/api/books/translation/settings/', 
            headers=headers, 
            json=update_data
        )
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("更新后的设置:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 验证更新是否生效
            if data.get('immersive_split_ratio') == 0.6:
                print("✅ 沉浸式分栏比例更新成功")
            else:
                print("❌ 沉浸式分栏比例更新失败")
        else:
            print(f"❌ 更新翻译设置失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 3. 测试翻译API
    print("\n3. 测试基本翻译功能...")
    try:
        translation_data = {
            'text': 'Hello, world!',
            'target_language': 'zh-CN',
            'source_language': 'en'
        }
        
        response = requests.post(
            f'{base_url}/api/books/translate/', 
            headers=headers, 
            json=translation_data
        )
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("翻译结果:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            if data.get('success') and data.get('translation'):
                print("✅ 翻译功能正常")
            else:
                print("❌ 翻译功能异常")
        else:
            print(f"❌ 翻译请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == '__main__':
    # 首先测试数据库连接
    try:
        from apps.books.models import TranslationSettings
        print("✅ Django环境初始化成功")
        
        # 检查模型字段
        settings_fields = [field.name for field in TranslationSettings._meta.fields]
        print(f"TranslationSettings模型字段: {settings_fields}")
        
        immersive_fields = ['immersive_enabled', 'immersive_auto_translate', 'immersive_split_ratio']
        missing_model_fields = []
        
        for field in immersive_fields:
            if field not in settings_fields:
                missing_model_fields.append(field)
        
        if missing_model_fields:
            print(f"⚠️  模型中缺少字段: {missing_model_fields}")
        else:
            print("✅ 所有沉浸式翻译字段在模型中都存在")
        
        # 测试API
        test_translation_settings_api()
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
