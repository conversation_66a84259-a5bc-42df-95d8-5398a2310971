import React, { useState, useEffect, useRef } from 'react';
import { translationAPI } from '@/lib/translationAPI';

interface ImmersiveTranslationProps {
  enabled: boolean;
  content: string;
  bookId?: string;
  chapterId?: string;
  splitRatio?: number;
  autoTranslate?: boolean;
  targetLanguage?: string;
  sourceLanguage?: string;
  onToggle: () => void;
}

interface TranslatedParagraph {
  original: string;
  translated: string;
  loading: boolean;
  error?: string;
}

const LANGUAGE_OPTIONS = [
  { value: 'auto', label: '自动检测' },
  { value: 'zh-CN', label: '中文' },
  { value: 'en', label: 'English' },
  { value: 'ja', label: '日本語' },
  { value: 'ko', label: '한국어' },
  { value: 'fr', label: 'Français' },
  { value: 'de', label: 'Deutsch' },
  { value: 'es', label: 'Español' },
];

const ImmersiveTranslation: React.FC<ImmersiveTranslationProps> = ({
  enabled,
  content,
  bookId,
  chapterId,
  splitRatio = 0.5,
  autoTranslate = false,
  targetLanguage = 'zh-CN',
  sourceLanguage = 'auto',
  onToggle
}) => {
  const [paragraphs, setParagraphs] = useState<TranslatedParagraph[]>([]);
  const [translating, setTranslating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentSourceLang, setCurrentSourceLang] = useState(sourceLanguage);
  const [currentTargetLang, setCurrentTargetLang] = useState(targetLanguage);
  const containerRef = useRef<HTMLDivElement>(null);

  // 分割内容为段落
  const splitIntoParagraphs = (text: string): string[] => {
    return text
      .split(/\n\s*\n/)
      .map(p => p.trim())
      .filter(p => p.length > 0);
  };

  // 翻译单个段落
  const translateParagraph = async (text: string, index: number): Promise<void> => {
    try {
      setParagraphs(prev => prev.map((p, i) => 
        i === index ? { ...p, loading: true, error: undefined } : p
      ));

      console.log(`正在翻译第${index}段落:`, text.substring(0, 50) + '...');
      
      const response = await translationAPI.translate({
        text: text.trim(),
        source_language: currentSourceLang,
        target_language: currentTargetLang,
        book_id: bookId,
        chapter_id: chapterId,
        position: index
      });
      
      console.log(`第${index}段落翻译响应:`, {
        success: response.success,
        translation: response.translation,
        responseKeys: Object.keys(response),
        fullResponse: response
      });

      if (response.success) {
        const translatedText = response.translation || response.data?.translation || '翻译结果为空';
        console.log(`第${index}段落翻译成功:`, translatedText);
        
        setParagraphs(prev => prev.map((p, i) =>
          i === index ? {
            ...p,
            translated: translatedText,
            loading: false
          } : p
        ));
      } else {
        const errorMsg = response.error || '翻译失败';
        console.error(`第${index}段落翻译失败:`, errorMsg);
        
        setParagraphs(prev => prev.map((p, i) =>
          i === index ? {
            ...p,
            loading: false,
            error: errorMsg
          } : p
        ));
      }
    } catch (error) {
      setParagraphs(prev => prev.map((p, i) => 
        i === index ? { 
          ...p, 
          loading: false, 
          error: '翻译错误' 
        } : p
      ));
    }
  };

  // 批量翻译所有段落
  const translateAllParagraphs = async () => {
    if (translating) return;
    
    setTranslating(true);
    setProgress(0);

    const originalParagraphs = splitIntoParagraphs(content);
    
    // 初始化段落状态
    setParagraphs(originalParagraphs.map(p => ({
      original: p,
      translated: '',
      loading: false
    })));

    // 逐个翻译段落
    for (let i = 0; i < originalParagraphs.length; i++) {
      await translateParagraph(originalParagraphs[i], i);
      setProgress(((i + 1) / originalParagraphs.length) * 100);
      
      // 添加小延迟避免API限制
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    setTranslating(false);
  };

  // 内容变化时重新初始化
  useEffect(() => {
    console.log('沉浸式翻译组件 useEffect:', { enabled, content: content?.substring(0, 100) + '...', autoTranslate });
    if (enabled && content) {
      const originalParagraphs = splitIntoParagraphs(content);
      console.log('分割后的段落数量:', originalParagraphs.length);
      setParagraphs(originalParagraphs.map(p => ({
        original: p,
        translated: '',
        loading: false
      })));

      if (autoTranslate) {
        translateAllParagraphs();
      }
    }
  }, [enabled, content, autoTranslate]);

  // 语言变化时清空已翻译内容
  useEffect(() => {
    if (enabled) {
      setParagraphs(prev => prev.map(p => ({
        ...p,
        translated: '',
        loading: false,
        error: undefined
      })));
    }
  }, [currentSourceLang, currentTargetLang]);

  console.log('沉浸式翻译组件渲染:', { enabled, contentLength: content?.length, paragraphsCount: paragraphs.length });

  if (!enabled) {
    console.log('沉浸式翻译未启用');
    return null;
  }

  const leftWidth = `${splitRatio * 100}%`;
  const rightWidth = `${(1 - splitRatio) * 100}%`;

  return (
    <div 
      className="fixed inset-0 bg-white z-[9999] flex flex-col" 
      ref={containerRef}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 9999,
        backgroundColor: 'white'
      }}
    >
      {/* 控制栏 */}
      <div className="bg-white border-b border-gray-200 p-3 flex items-center justify-between shadow-sm flex-shrink-0">
        <div className="flex items-center space-x-4">
          <div className="text-lg font-bold text-blue-600">
            🌍 沉浸式翻译模式
          </div>
          <button
            onClick={onToggle}
            className="flex items-center space-x-2 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
            <span>退出沉浸式</span>
          </button>

          {/* 语言选择器 */}
          <div className="flex items-center space-x-2">
            <select
              value={currentSourceLang}
              onChange={(e) => setCurrentSourceLang(e.target.value)}
              className="px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={translating}
            >
              {LANGUAGE_OPTIONS.map(lang => (
                <option key={lang.value} value={lang.value}>
                  {lang.label}
                </option>
              ))}
            </select>

            <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>

            <select
              value={currentTargetLang}
              onChange={(e) => setCurrentTargetLang(e.target.value)}
              className="px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={translating}
            >
              {LANGUAGE_OPTIONS.filter(lang => lang.value !== 'auto').map(lang => (
                <option key={lang.value} value={lang.value}>
                  {lang.label}
                </option>
              ))}
            </select>
          </div>

          <button
            onClick={translateAllParagraphs}
            disabled={translating}
            className="flex items-center space-x-2 px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-400 transition-colors"
          >
            {translating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>翻译中... {Math.round(progress)}%</span>
              </>
            ) : (
              <>
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                </svg>
                <span>翻译全部</span>
              </>
            )}
          </button>
        </div>

        <div className="text-sm text-gray-600">
          {paragraphs.length} 段落 | {paragraphs.filter(p => p.translated).length} 已翻译
        </div>
      </div>

      {/* 双栏内容 */}
      <div className="flex-1 flex overflow-hidden">
        {/* 原文栏 */}
        <div 
          className="border-r border-gray-200 overflow-y-auto bg-gray-50"
          style={{ width: leftWidth }}
        >
          <div className="p-6">
            <h3 className="text-lg font-semibold mb-4 text-gray-700 sticky top-0 bg-white py-2">
              原文
            </h3>
            <div className="space-y-4">
              {paragraphs.map((paragraph, index) => (
                <div 
                  key={index}
                  className="p-3 rounded-lg bg-white hover:bg-gray-100 transition-all cursor-pointer border border-gray-200 hover:border-gray-300 hover:shadow-sm"
                  onClick={() => translateParagraph(paragraph.original, index)}
                >
                  <div className="text-sm text-gray-800 leading-relaxed whitespace-pre-wrap">
                    {paragraph.original}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 译文栏 */}
        <div 
          className="overflow-y-auto bg-blue-50"
          style={{ width: rightWidth }}
        >
          <div className="p-6">
            <h3 className="text-lg font-semibold mb-4 text-blue-700 sticky top-0 bg-white py-2">
              译文
            </h3>
            <div className="space-y-4">
              {paragraphs.map((paragraph, index) => (
                <div 
                  key={index}
                  className="p-3 rounded-lg bg-white min-h-[60px] flex items-center border border-blue-200"
                >
                  {paragraph.loading ? (
                    <div className="flex items-center justify-center w-full py-4">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                      <span className="ml-2 text-sm text-gray-500">翻译中...</span>
                    </div>
                  ) : paragraph.error ? (
                    <div className="text-sm text-red-500 w-full text-center">
                      {paragraph.error}
                      <button 
                        onClick={() => translateParagraph(paragraph.original, index)}
                        className="ml-2 text-blue-500 hover:underline"
                      >
                        重试
                      </button>
                    </div>
                  ) : paragraph.translated ? (
                    <div className="text-sm text-blue-800 leading-relaxed whitespace-pre-wrap">
                      {paragraph.translated}
                    </div>
                  ) : (
                    <div className="text-sm text-gray-400 w-full text-center">
                      点击左侧段落进行翻译
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImmersiveTranslation;
