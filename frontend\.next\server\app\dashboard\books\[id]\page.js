/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/books/[id]/page";
exports.ids = ["app/dashboard/books/[id]/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fbooks%2F%5Bid%5D%2Fpage&page=%2Fdashboard%2Fbooks%2F%5Bid%5D%2Fpage&appPaths=%2Fdashboard%2Fbooks%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fbooks%2F%5Bid%5D%2Fpage.tsx&appDir=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fbooks%2F%5Bid%5D%2Fpage&page=%2Fdashboard%2Fbooks%2F%5Bid%5D%2Fpage&appPaths=%2Fdashboard%2Fbooks%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fbooks%2F%5Bid%5D%2Fpage.tsx&appDir=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'books',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/books/[id]/page.tsx */ \"(rsc)/./src/app/dashboard/books/[id]/page.tsx\")), \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/books/[id]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/books/[id]/page\",\n        pathname: \"/dashboard/books/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fbooks%2F%5Bid%5D%2Fpage&page=%2Fdashboard%2Fbooks%2F%5Bid%5D%2Fpage&appPaths=%2Fdashboard%2Fbooks%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fbooks%2F%5Bid%5D%2Fpage.tsx&appDir=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Cstyles%5Creader-themes.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Cstyles%5Creader-themes.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RSUzQSU1Q2F1Z21lbnQtdGVzdCU1Q3V0aWwlNUNCb29rUmVhbG0lNUNmcm9udGVuZCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUUlM0ElNUNhdWdtZW50LXRlc3QlNUN1dGlsJTVDQm9va1JlYWxtJTVDZnJvbnRlbmQlNUNub2RlX21vZHVsZXMlNUNyZWFjdC1ob3QtdG9hc3QlNUNkaXN0JTVDaW5kZXgubWpzJm1vZHVsZXM9RSUzQSU1Q2F1Z21lbnQtdGVzdCU1Q3V0aWwlNUNCb29rUmVhbG0lNUNmcm9udGVuZCU1Q3NyYyU1Q2FwcCU1Q2dsb2JhbHMuY3NzJm1vZHVsZXM9RSUzQSU1Q2F1Z21lbnQtdGVzdCU1Q3V0aWwlNUNCb29rUmVhbG0lNUNmcm9udGVuZCU1Q3NyYyU1Q2NvbnRleHRzJTVDQXV0aENvbnRleHQudHN4Jm1vZHVsZXM9RSUzQSU1Q2F1Z21lbnQtdGVzdCU1Q3V0aWwlNUNCb29rUmVhbG0lNUNmcm9udGVuZCU1Q3NyYyU1Q3N0eWxlcyU1Q3JlYWRlci10aGVtZXMuY3NzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTUFBZ0k7QUFDaEkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ib29rcmVhbG0tZnJvbnRlbmQvPzZhMDIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxhdWdtZW50LXRlc3RcXFxcdXRpbFxcXFxCb29rUmVhbG1cXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcYXVnbWVudC10ZXN0XFxcXHV0aWxcXFxcQm9va1JlYWxtXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxjb250ZXh0c1xcXFxBdXRoQ29udGV4dC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Cstyles%5Creader-themes.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp%5Cdashboard%5Cbooks%5C%5Bid%5D%5Cpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp%5Cdashboard%5Cbooks%5C%5Bid%5D%5Cpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/books/[id]/page.tsx */ \"(ssr)/./src/app/dashboard/books/[id]/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RSUzQSU1Q2F1Z21lbnQtdGVzdCU1Q3V0aWwlNUNCb29rUmVhbG0lNUNmcm9udGVuZCU1Q3NyYyU1Q2FwcCU1Q2Rhc2hib2FyZCU1Q2Jvb2tzJTVDJTVCaWQlNUQlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ib29rcmVhbG0tZnJvbnRlbmQvP2FmNTQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxhdWdtZW50LXRlc3RcXFxcdXRpbFxcXFxCb29rUmVhbG1cXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcYm9va3NcXFxcW2lkXVxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp%5Cdashboard%5Cbooks%5C%5Bid%5D%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/books/[id]/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/dashboard/books/[id]/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BookDetailPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_bookApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/bookApi */ \"(ssr)/./src/lib/bookApi.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,BookOpen,Calendar,Eye,FileText,HardDrive,Play,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,BookOpen,Calendar,Eye,FileText,HardDrive,Play,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,BookOpen,Calendar,Eye,FileText,HardDrive,Play,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,BookOpen,Calendar,Eye,FileText,HardDrive,Play,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,BookOpen,Calendar,Eye,FileText,HardDrive,Play,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,BookOpen,Calendar,Eye,FileText,HardDrive,Play,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,BookOpen,Calendar,Eye,FileText,HardDrive,Play,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,BookOpen,Calendar,Eye,FileText,HardDrive,Play,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,BookOpen,Calendar,Eye,FileText,HardDrive,Play,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,BookOpen,Calendar,Eye,FileText,HardDrive,Play,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction BookDetailPage() {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const bookId = params.id;\n    const [book, setBook] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [deleting, setDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 获取书籍详情\n    const fetchBookDetail = async ()=>{\n        try {\n            setLoading(true);\n            const [bookResponse, statsResponse] = await Promise.all([\n                _lib_bookApi__WEBPACK_IMPORTED_MODULE_4__.bookAPI.getBookDetail(bookId),\n                _lib_bookApi__WEBPACK_IMPORTED_MODULE_4__.bookAPI.getBookStats(bookId)\n            ]);\n            if (bookResponse.success) {\n                setBook(bookResponse.data);\n            }\n            if (statsResponse.success) {\n                setStats(statsResponse.data);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch book detail:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error(\"获取书籍详情失败\");\n            router.push(\"/dashboard/books\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 删除书籍\n    const handleDeleteBook = async ()=>{\n        if (!book) return;\n        if (!confirm(`确定要删除《${book.title}》吗？此操作不可恢复。`)) {\n            return;\n        }\n        try {\n            setDeleting(true);\n            const response = await _lib_bookApi__WEBPACK_IMPORTED_MODULE_4__.bookAPI.deleteBook(book.id);\n            if (response.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success(response.message);\n                router.push(\"/dashboard/books\");\n            }\n        } catch (error) {\n            console.error(\"Failed to delete book:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error(\"删除书籍失败\");\n        } finally{\n            setDeleting(false);\n        }\n    };\n    // 开始阅读\n    const handleStartReading = async ()=>{\n        if (!book || !book.chapters || book.chapters.length === 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error(\"该书籍暂无可阅读的章节\");\n            return;\n        }\n        try {\n            // 获取最新的阅读进度\n            const progressResponse = await _lib_bookApi__WEBPACK_IMPORTED_MODULE_4__.bookAPI.getReadingProgress(book.id);\n            let targetChapter = book.chapters[0] // 默认第一章\n            ;\n            if (progressResponse.success && progressResponse.data.has_progress) {\n                // 如果有阅读进度，找到对应的章节\n                const progressData = progressResponse.data;\n                if (progressData.current_chapter_id) {\n                    const foundChapter = book.chapters.find((c)=>c.id === progressData.current_chapter_id);\n                    if (foundChapter) {\n                        targetChapter = foundChapter;\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success(`继续阅读：${foundChapter.title}`);\n                    }\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success(\"开始新的阅读之旅！\");\n            }\n            router.push(`/dashboard/reader/${book.id}/${targetChapter.id}`);\n        } catch (error) {\n            console.error(\"Failed to get reading progress:\", error);\n            // 如果获取进度失败，直接跳转到第一章\n            router.push(`/dashboard/reader/${book.id}/${book.chapters[0].id}`);\n        }\n    };\n    // 格式化文件大小\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    // 格式化数字\n    const formatNumber = (num)=>{\n        if (num >= 10000) {\n            return (num / 10000).toFixed(1) + \"万\";\n        }\n        return num.toLocaleString();\n    };\n    // 格式化日期\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"zh-CN\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    };\n    // 计算阅读进度百分比\n    const getReadingProgressPercentage = ()=>{\n        if (!stats?.reading_progress || !book) return 0;\n        return Math.round(stats.reading_progress.current_chapter / book.total_chapters * 100);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && bookId) {\n            fetchBookDetail();\n        }\n    }, [\n        user,\n        bookId\n    ]);\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"bg-white shadow-sm border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: \"/dashboard/books\",\n                                className: \"flex items-center text-gray-500 hover:text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"返回书库\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n            lineNumber: 154,\n            columnNumber: 7\n        }, this);\n    }\n    if (!book) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"bg-white shadow-sm border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                href: \"/dashboard/books\",\n                                className: \"flex items-center text-gray-500 hover:text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"返回书库\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"书籍不存在\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"该书籍可能已被删除或您没有访问权限\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n            lineNumber: 177,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/dashboard/books\",\n                                        className: \"flex items-center text-gray-500 hover:text-gray-700 mr-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"返回书库\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-900 truncate\",\n                                        children: book.title\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleStartReading,\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            stats?.reading_progress ? \"继续阅读\" : \"开始阅读\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDeleteBook,\n                                        disabled: deleting,\n                                        className: \"inline-flex items-center px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-sm p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-32 bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-12 w-12 text-primary-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                                        children: book.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    book.author && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center text-gray-600 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: book.author\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center text-gray-500 text-sm mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"上传于 \",\n                                                                    formatDate(book.uploaded_at)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 267,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            book.total_chapters,\n                                                                            \" 章节\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 268,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs mr-1\",\n                                                                        children: \"字\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 271,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatNumber(book.total_words)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: formatFileSize(book.file_size)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this),\n                                stats?.reading_progress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-sm p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"阅读进度\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-sm text-gray-600 mb-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"整体进度\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 293,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        getReadingProgressPercentage(),\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-primary-600 h-2 rounded-full transition-all duration-300\",\n                                                                style: {\n                                                                    width: `${getReadingProgressPercentage()}%`\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"当前章节\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        \"第 \",\n                                                                        stats.reading_progress.current_chapter,\n                                                                        \" 章\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-500\",\n                                                                    children: \"上次阅读\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-medium\",\n                                                                    children: new Date(stats.reading_progress.last_read_at).toLocaleDateString(\"zh-CN\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-sm p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-4\",\n                                            children: \"统计信息\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: \"总章节\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: book.total_chapters\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: \"总字数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: formatNumber(book.total_words)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: \"书签数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: stats?.bookmark_count || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500\",\n                                                            children: \"文件大小\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: formatFileSize(book.file_size)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 border-b border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"章节目录\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-96 overflow-y-auto\",\n                                            children: book.chapters && book.chapters.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"divide-y divide-gray-200\",\n                                                children: book.chapters.map((chapter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 hover:bg-gray-50 cursor-pointer transition-colors\",\n                                                        onClick: ()=>router.push(`/dashboard/reader/${book.id}/${chapter.id}`),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                                            children: chapter.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 364,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                formatNumber(chapter.word_count),\n                                                                                \" 字\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 367,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-shrink-0 ml-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 372,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, chapter.id, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 text-center text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_BookOpen_Calendar_Eye_FileText_HardDrive_Play_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"mx-auto h-8 w-8 text-gray-400 mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"暂无章节内容\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\[id]\\\\page.tsx\",\n        lineNumber: 201,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/books/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 检查用户认证状态\n    const checkAuth = async ()=>{\n        try {\n            const token = (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.getAccessToken)();\n            if (!token) {\n                setLoading(false);\n                return;\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authAPI.verifyToken();\n            if (response.user) {\n                setUser(response.user);\n            }\n        } catch (error) {\n            console.error(\"Auth check failed:\", error);\n            (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.clearAuthTokens)();\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 用户登录\n    const login = async (email, password)=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authAPI.login({\n                email,\n                password\n            });\n            if (response.tokens && response.user) {\n                (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.setAuthTokens)(response.tokens);\n                setUser(response.user);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.message || \"登录成功\");\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            // 处理详细的错误信息\n            let message = \"登录失败\";\n            if (error.response?.data) {\n                const errorData = error.response.data;\n                if (errorData.message) {\n                    message = errorData.message;\n                } else if (errorData.errors) {\n                    // 处理字段验证错误\n                    const errorMessages = [];\n                    for (const [field, fieldErrors] of Object.entries(errorData.errors)){\n                        if (Array.isArray(fieldErrors)) {\n                            errorMessages.push(`${field}: ${fieldErrors.join(\", \")}`);\n                        } else {\n                            errorMessages.push(`${field}: ${fieldErrors}`);\n                        }\n                    }\n                    message = errorMessages.join(\"; \");\n                }\n            } else if (error.message) {\n                message = error.message;\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(message);\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 用户注册\n    const register = async (data)=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authAPI.register(data);\n            if (response.tokens && response.user) {\n                (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.setAuthTokens)(response.tokens);\n                setUser(response.user);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.message || \"注册成功\");\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error(\"Registration failed:\", error);\n            // 处理详细的错误信息\n            let message = \"注册失败\";\n            if (error.response?.data) {\n                const errorData = error.response.data;\n                if (errorData.message) {\n                    message = errorData.message;\n                } else if (errorData.errors) {\n                    // 处理字段验证错误\n                    const errorMessages = [];\n                    for (const [field, fieldErrors] of Object.entries(errorData.errors)){\n                        let fieldName = field;\n                        // 翻译字段名\n                        const fieldTranslations = {\n                            \"username\": \"用户名\",\n                            \"email\": \"邮箱\",\n                            \"password\": \"密码\",\n                            \"password_confirm\": \"确认密码\",\n                            \"display_name\": \"显示名称\"\n                        };\n                        if (fieldTranslations[field]) {\n                            fieldName = fieldTranslations[field];\n                        }\n                        if (Array.isArray(fieldErrors)) {\n                            errorMessages.push(`${fieldName}: ${fieldErrors.join(\", \")}`);\n                        } else {\n                            errorMessages.push(`${fieldName}: ${fieldErrors}`);\n                        }\n                    }\n                    message = errorMessages.join(\"\\n\");\n                }\n            } else if (error.message) {\n                message = error.message;\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(message, {\n                duration: 6000,\n                style: {\n                    whiteSpace: \"pre-line\"\n                }\n            });\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 用户登出\n    const logout = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authAPI.logout();\n        } catch (error) {\n            console.error(\"Logout API failed:\", error);\n        } finally{\n            (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.clearAuthTokens)();\n            setUser(null);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"已退出登录\");\n        }\n    };\n    // 更新用户资料\n    const updateProfile = async (data)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authAPI.updateProfile(data);\n            if (response.user) {\n                setUser(response.user);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.message || \"资料更新成功\");\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error(\"Profile update failed:\", error);\n            const message = error.response?.data?.message || \"资料更新失败\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(message);\n            return false;\n        }\n    };\n    // 修改密码\n    const changePassword = async (data)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authAPI.changePassword(data);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.message || \"密码修改成功\");\n            return true;\n        } catch (error) {\n            console.error(\"Password change failed:\", error);\n            const message = error.response?.data?.message || \"密码修改失败\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(message);\n            return false;\n        }\n    };\n    // 组件挂载时检查认证状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkAuth();\n    }, []);\n    const value = {\n        user,\n        loading,\n        login,\n        register,\n        logout,\n        updateProfile,\n        changePassword\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 257,\n        columnNumber: 10\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authAPI: () => (/* binding */ authAPI),\n/* harmony export */   clearAuthTokens: () => (/* binding */ clearAuthTokens),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getRefreshToken: () => (/* binding */ getRefreshToken),\n/* harmony export */   setAuthTokens: () => (/* binding */ setAuthTokens)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n\n\n// 创建 axios 实例\nconst api = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL:  false ? 0 : \"http://127.0.0.1:8000/api\",\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// 请求拦截器 - 添加认证 token\napi.interceptors.request.use((config)=>{\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器 - 处理错误和 token 刷新\napi.interceptors.response.use((response)=>{\n    return response;\n}, async (error)=>{\n    const originalRequest = error.config;\n    // 如果是 401 错误且不是刷新 token 的请求\n    if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            const refreshToken = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"refresh_token\");\n            if (refreshToken) {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(`${api.defaults.baseURL}/auth/token/refresh/`, {\n                    refresh: refreshToken\n                });\n                const { access } = response.data;\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"access_token\", access, {\n                    expires: 1\n                }) // 1天过期\n                ;\n                // 重新发送原请求\n                originalRequest.headers.Authorization = `Bearer ${access}`;\n                return api(originalRequest);\n            }\n        } catch (refreshError) {\n            // 刷新失败，清除所有 token 并跳转到登录页\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"access_token\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"refresh_token\");\n            window.location.href = \"/auth/login\";\n            return Promise.reject(refreshError);\n        }\n    }\n    // 显示错误消息 - 不在这里显示，让组件自己处理\n    // 这样可以提供更详细的错误信息\n    return Promise.reject(error);\n});\n// 用户认证相关 API\nconst authAPI = {\n    // 用户注册\n    register: async (data)=>{\n        const response = await api.post(\"/auth/register/\", data);\n        return response.data;\n    },\n    // 用户登录\n    login: async (data)=>{\n        const response = await api.post(\"/auth/login/\", data);\n        return response.data;\n    },\n    // 获取用户资料\n    getProfile: async ()=>{\n        const response = await api.get(\"/auth/profile/\");\n        return response.data;\n    },\n    // 更新用户资料\n    updateProfile: async (data)=>{\n        const response = await api.put(\"/auth/profile/\", data);\n        return response.data;\n    },\n    // 修改密码\n    changePassword: async (data)=>{\n        const response = await api.post(\"/auth/password/change/\", data);\n        return response.data;\n    },\n    // 用户登出\n    logout: async ()=>{\n        const response = await api.post(\"/auth/logout/\");\n        return response.data;\n    },\n    // 验证 token\n    verifyToken: async ()=>{\n        const response = await api.get(\"/auth/token/verify/\");\n        return response.data;\n    }\n};\n// 工具函数\nconst setAuthTokens = (tokens)=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"access_token\", tokens.access, {\n        expires: 1\n    }) // 1天过期\n    ;\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"refresh_token\", tokens.refresh, {\n        expires: 7\n    }) // 7天过期\n    ;\n};\nconst clearAuthTokens = ()=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"access_token\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"refresh_token\");\n};\nconst getAccessToken = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n};\nconst getRefreshToken = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"refresh_token\");\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/bookApi.ts":
/*!****************************!*\
  !*** ./src/lib/bookApi.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bookAPI: () => (/* binding */ bookAPI)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"(ssr)/./src/lib/api.ts\");\n\n// 书籍相关 API\nconst bookAPI = {\n    // 上传书籍\n    uploadBook: async (formData, onUploadProgress)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/books/upload/\", formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            },\n            onUploadProgress: (progressEvent)=>{\n                if (onUploadProgress && progressEvent.total) {\n                    const progress = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n                    onUploadProgress(progress);\n                }\n            }\n        });\n        return response.data;\n    },\n    // 获取书籍列表\n    getBooks: async ()=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/books/\");\n        return response.data;\n    },\n    // 获取书籍详情\n    getBookDetail: async (bookId)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/books/${bookId}/`);\n        return response.data;\n    },\n    // 删除书籍\n    deleteBook: async (bookId)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(`/books/${bookId}/delete/`);\n        return response.data;\n    },\n    // 获取章节内容\n    getChapterContent: async (bookId, chapterId)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/books/${bookId}/chapters/${chapterId}/`);\n        return response.data;\n    },\n    // 获取书籍统计\n    getBookStats: async (bookId)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/books/${bookId}/stats/`);\n        return response.data;\n    },\n    // 更新阅读进度\n    updateReadingProgress: async (bookId, chapterId, position = 0)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/books/${bookId}/progress/`, {\n            chapter_id: chapterId,\n            position: position\n        });\n        return response.data;\n    },\n    // 获取阅读进度\n    getReadingProgress: async (bookId)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/books/${bookId}/progress/get/`);\n        return response.data;\n    },\n    // 获取阅读器设置\n    getReaderSettings: async ()=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/books/reader-settings/\");\n        return response.data;\n    },\n    // 更新阅读器设置\n    updateReaderSettings: async (settings)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/books/reader-settings/\", settings);\n        return response.data;\n    },\n    // 重置阅读器设置\n    resetReaderSettings: async ()=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/books/reader-settings/\");\n        return response.data;\n    },\n    // 书签管理\n    // 获取书签列表\n    getBookmarks: async (bookId, page = 1, pageSize = 20)=>{\n        const url = bookId ? `/books/${bookId}/bookmarks/` : \"/books/bookmarks/\";\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n            params: {\n                page,\n                page_size: pageSize\n            }\n        });\n        return response.data;\n    },\n    // 创建书签\n    createBookmark: async (bookId, bookmarkData)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/books/${bookId}/bookmarks/`, bookmarkData);\n        return response.data;\n    },\n    // 获取书签详情\n    getBookmarkDetail: async (bookmarkId)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/books/bookmarks/${bookmarkId}/`);\n        return response.data;\n    },\n    // 更新书签\n    updateBookmark: async (bookmarkId, bookmarkData)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`/books/bookmarks/${bookmarkId}/update/`, bookmarkData);\n        return response.data;\n    },\n    // 删除书签\n    deleteBookmark: async (bookmarkId)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(`/books/bookmarks/${bookmarkId}/delete/`);\n        return response.data;\n    },\n    // 生成章节总结\n    generateChapterSummary: async (bookId, chapterId, language = \"zh-CN\")=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/books/${bookId}/chapters/${chapterId}/summary/`, {\n            language: language\n        });\n        return response.data;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/bookApi.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"179f011296ba\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYm9va3JlYWxtLWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9jYmYzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMTc5ZjAxMTI5NmJhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/reader-themes.css":
/*!**************************************!*\
  !*** ./src/styles/reader-themes.css ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ce510e795c69\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL3JlYWRlci10aGVtZXMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYm9va3JlYWxtLWZyb250ZW5kLy4vc3JjL3N0eWxlcy9yZWFkZXItdGhlbWVzLmNzcz8xYjc4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiY2U1MTBlNzk1YzY5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/reader-themes.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/books/[id]/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/dashboard/books/[id]/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\augment-test\util\BookRealm\frontend\src\app\dashboard\books\[id]\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _styles_reader_themes_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/reader-themes.css */ \"(rsc)/./src/styles/reader-themes.css\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"BookRealm - 电子书阅读平台\",\n    description: \"智能电子书阅读平台，支持TXT文件解析、AI翻译等功能\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.AuthProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e1),
/* harmony export */   useAuth: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\augment-test\util\BookRealm\frontend\src\contexts\AuthContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\augment-test\util\BookRealm\frontend\src\contexts\AuthContext.tsx#useAuth`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\augment-test\util\BookRealm\frontend\src\contexts\AuthContext.tsx#AuthProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/react-hot-toast","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/js-cookie","vendor-chunks/proxy-from-env","vendor-chunks/goober","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/es-define-property","vendor-chunks/gopd","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fbooks%2F%5Bid%5D%2Fpage&page=%2Fdashboard%2Fbooks%2F%5Bid%5D%2Fpage&appPaths=%2Fdashboard%2Fbooks%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fbooks%2F%5Bid%5D%2Fpage.tsx&appDir=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();