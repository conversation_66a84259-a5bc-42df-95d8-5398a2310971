/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/books/page";
exports.ids = ["app/dashboard/books/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fbooks%2Fpage&page=%2Fdashboard%2Fbooks%2Fpage&appPaths=%2Fdashboard%2Fbooks%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fbooks%2Fpage.tsx&appDir=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fbooks%2Fpage&page=%2Fdashboard%2Fbooks%2Fpage&appPaths=%2Fdashboard%2Fbooks%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fbooks%2Fpage.tsx&appDir=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'books',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/books/page.tsx */ \"(rsc)/./src/app/dashboard/books/page.tsx\")), \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/books/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/books/page\",\n        pathname: \"/dashboard/books\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fbooks%2Fpage&page=%2Fdashboard%2Fbooks%2Fpage&appPaths=%2Fdashboard%2Fbooks%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fbooks%2Fpage.tsx&appDir=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Cstyles%5Creader-themes.css&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Cstyles%5Creader-themes.css&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RSUzQSU1Q2F1Z21lbnQtdGVzdCU1Q3V0aWwlNUNCb29rUmVhbG0lNUNmcm9udGVuZCU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUUlM0ElNUNhdWdtZW50LXRlc3QlNUN1dGlsJTVDQm9va1JlYWxtJTVDZnJvbnRlbmQlNUNub2RlX21vZHVsZXMlNUNyZWFjdC1ob3QtdG9hc3QlNUNkaXN0JTVDaW5kZXgubWpzJm1vZHVsZXM9RSUzQSU1Q2F1Z21lbnQtdGVzdCU1Q3V0aWwlNUNCb29rUmVhbG0lNUNmcm9udGVuZCU1Q3NyYyU1Q2FwcCU1Q2dsb2JhbHMuY3NzJm1vZHVsZXM9RSUzQSU1Q2F1Z21lbnQtdGVzdCU1Q3V0aWwlNUNCb29rUmVhbG0lNUNmcm9udGVuZCU1Q3NyYyU1Q2NvbnRleHRzJTVDQXV0aENvbnRleHQudHN4Jm1vZHVsZXM9RSUzQSU1Q2F1Z21lbnQtdGVzdCU1Q3V0aWwlNUNCb29rUmVhbG0lNUNmcm9udGVuZCU1Q3NyYyU1Q3N0eWxlcyU1Q3JlYWRlci10aGVtZXMuY3NzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTUFBZ0k7QUFDaEkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ib29rcmVhbG0tZnJvbnRlbmQvPzZhMDIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxhdWdtZW50LXRlc3RcXFxcdXRpbFxcXFxCb29rUmVhbG1cXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcYXVnbWVudC10ZXN0XFxcXHV0aWxcXFxcQm9va1JlYWxtXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxjb250ZXh0c1xcXFxBdXRoQ29udGV4dC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp%5Cglobals.css&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Ccontexts%5CAuthContext.tsx&modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Cstyles%5Creader-themes.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp%5Cdashboard%5Cbooks%5Cpage.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp%5Cdashboard%5Cbooks%5Cpage.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/books/page.tsx */ \"(ssr)/./src/app/dashboard/books/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RSUzQSU1Q2F1Z21lbnQtdGVzdCU1Q3V0aWwlNUNCb29rUmVhbG0lNUNmcm9udGVuZCU1Q3NyYyU1Q2FwcCU1Q2Rhc2hib2FyZCU1Q2Jvb2tzJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYm9va3JlYWxtLWZyb250ZW5kLz85ZmQwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcYXVnbWVudC10ZXN0XFxcXHV0aWxcXFxcQm9va1JlYWxtXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXGJvb2tzXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp%5Cdashboard%5Cbooks%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/books/page.tsx":
/*!******************************************!*\
  !*** ./src/app/dashboard/books/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BooksPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_bookApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/bookApi */ \"(ssr)/./src/lib/bookApi.ts\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,FileText,Plus,Search,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,FileText,Plus,Search,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,FileText,Plus,Search,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,FileText,Plus,Search,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,FileText,Plus,Search,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,FileText,Plus,Search,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,FileText,Plus,Search,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,FileText,Plus,Search,Trash2,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction BooksPage() {\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [books, setBooks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredBooks, setFilteredBooks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 获取书籍列表\n    const fetchBooks = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _lib_bookApi__WEBPACK_IMPORTED_MODULE_3__.bookAPI.getBooks();\n            if (response.success) {\n                setBooks(response.data.books);\n                setFilteredBooks(response.data.books);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch books:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"获取书籍列表失败\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 删除书籍\n    const handleDeleteBook = async (bookId, title)=>{\n        if (!confirm(`确定要删除《${title}》吗？此操作不可恢复。`)) {\n            return;\n        }\n        try {\n            const response = await _lib_bookApi__WEBPACK_IMPORTED_MODULE_3__.bookAPI.deleteBook(bookId);\n            if (response.success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].success(response.message);\n                // 从列表中移除已删除的书籍\n                const updatedBooks = books.filter((book)=>book.id !== bookId);\n                setBooks(updatedBooks);\n                setFilteredBooks(updatedBooks.filter((book)=>book.title.toLowerCase().includes(searchQuery.toLowerCase()) || book.author.toLowerCase().includes(searchQuery.toLowerCase())));\n            }\n        } catch (error) {\n            console.error(\"Failed to delete book:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_5__[\"default\"].error(\"删除书籍失败\");\n        }\n    };\n    // 搜索过滤\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!searchQuery) {\n            setFilteredBooks(books);\n        } else {\n            const filtered = books.filter((book)=>book.title.toLowerCase().includes(searchQuery.toLowerCase()) || book.author.toLowerCase().includes(searchQuery.toLowerCase()));\n            setFilteredBooks(filtered);\n        }\n    }, [\n        searchQuery,\n        books\n    ]);\n    // 组件挂载时获取数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user) {\n            fetchBooks();\n        }\n    }, [\n        user\n    ]);\n    // 格式化文件大小\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return \"0 Bytes\";\n        const k = 1024;\n        const sizes = [\n            \"Bytes\",\n            \"KB\",\n            \"MB\",\n            \"GB\"\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n    };\n    // 格式化数字\n    const formatNumber = (num)=>{\n        if (num >= 10000) {\n            return (num / 10000).toFixed(1) + \"万\";\n        }\n        return num.toLocaleString();\n    };\n    // 格式化日期\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"zh-CN\");\n    };\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-8 w-8 text-primary-600 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"我的书库\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"/dashboard/upload\",\n                                    className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"上传书籍\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: searchQuery,\n                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                    className: \"form-input-with-icon-left\",\n                                    placeholder: \"搜索书籍标题或作者...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this) : filteredBooks.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                children: searchQuery ? \"没有找到匹配的书籍\" : \"还没有上传任何书籍\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mb-6\",\n                                children: searchQuery ? \"尝试使用其他关键词搜索\" : \"上传您的第一本电子书开始阅读吧！\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this),\n                            !searchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                href: \"/dashboard/upload\",\n                                className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"上传书籍\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3\",\n                        children: filteredBooks.map((book)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 line-clamp-2\",\n                                                    children: book.title\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${book.status === \"ready\" ? \"bg-green-100 text-green-800\" : book.status === \"parsing\" ? \"bg-yellow-100 text-yellow-800\" : book.status === \"uploading\" ? \"bg-blue-100 text-blue-800\" : \"bg-red-100 text-red-800\"}`,\n                                                    children: book.status === \"ready\" ? \"就绪\" : book.status === \"parsing\" ? \"解析中\" : book.status === \"uploading\" ? \"上传中\" : \"错误\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, this),\n                                        book.author && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-sm text-gray-600 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 23\n                                                }, this),\n                                                book.author\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4 mb-4 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        book.total_chapters,\n                                                        \" 章节\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs mr-1\",\n                                                            children: \"字\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        formatNumber(book.total_words)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-xs text-gray-500 mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"阅读进度\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"0%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-200 rounded-full h-1.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-primary-600 h-1.5 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"0%\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"大小: \",\n                                                            formatFileSize(book.file_size)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            formatDate(book.uploaded_at)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 mb-4 truncate\",\n                                            children: [\n                                                \"文件: \",\n                                                book.filename\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                    href: `/dashboard/books/${book.id}`,\n                                                    className: \"flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"查看\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteBook(book.id, book.title),\n                                                    className: \"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_FileText_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 17\n                                }, this)\n                            }, book.id, false, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\books\\\\page.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/books/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // 检查用户认证状态\n    const checkAuth = async ()=>{\n        try {\n            const token = (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.getAccessToken)();\n            if (!token) {\n                setLoading(false);\n                return;\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authAPI.verifyToken();\n            if (response.user) {\n                setUser(response.user);\n            }\n        } catch (error) {\n            console.error(\"Auth check failed:\", error);\n            (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.clearAuthTokens)();\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 用户登录\n    const login = async (email, password)=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authAPI.login({\n                email,\n                password\n            });\n            if (response.tokens && response.user) {\n                (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.setAuthTokens)(response.tokens);\n                setUser(response.user);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.message || \"登录成功\");\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error(\"Login failed:\", error);\n            // 处理详细的错误信息\n            let message = \"登录失败\";\n            if (error.response?.data) {\n                const errorData = error.response.data;\n                if (errorData.message) {\n                    message = errorData.message;\n                } else if (errorData.errors) {\n                    // 处理字段验证错误\n                    const errorMessages = [];\n                    for (const [field, fieldErrors] of Object.entries(errorData.errors)){\n                        if (Array.isArray(fieldErrors)) {\n                            errorMessages.push(`${field}: ${fieldErrors.join(\", \")}`);\n                        } else {\n                            errorMessages.push(`${field}: ${fieldErrors}`);\n                        }\n                    }\n                    message = errorMessages.join(\"; \");\n                }\n            } else if (error.message) {\n                message = error.message;\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(message);\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 用户注册\n    const register = async (data)=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authAPI.register(data);\n            if (response.tokens && response.user) {\n                (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.setAuthTokens)(response.tokens);\n                setUser(response.user);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.message || \"注册成功\");\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error(\"Registration failed:\", error);\n            // 处理详细的错误信息\n            let message = \"注册失败\";\n            if (error.response?.data) {\n                const errorData = error.response.data;\n                if (errorData.message) {\n                    message = errorData.message;\n                } else if (errorData.errors) {\n                    // 处理字段验证错误\n                    const errorMessages = [];\n                    for (const [field, fieldErrors] of Object.entries(errorData.errors)){\n                        let fieldName = field;\n                        // 翻译字段名\n                        const fieldTranslations = {\n                            \"username\": \"用户名\",\n                            \"email\": \"邮箱\",\n                            \"password\": \"密码\",\n                            \"password_confirm\": \"确认密码\",\n                            \"display_name\": \"显示名称\"\n                        };\n                        if (fieldTranslations[field]) {\n                            fieldName = fieldTranslations[field];\n                        }\n                        if (Array.isArray(fieldErrors)) {\n                            errorMessages.push(`${fieldName}: ${fieldErrors.join(\", \")}`);\n                        } else {\n                            errorMessages.push(`${fieldName}: ${fieldErrors}`);\n                        }\n                    }\n                    message = errorMessages.join(\"\\n\");\n                }\n            } else if (error.message) {\n                message = error.message;\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(message, {\n                duration: 6000,\n                style: {\n                    whiteSpace: \"pre-line\"\n                }\n            });\n            return false;\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 用户登出\n    const logout = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authAPI.logout();\n        } catch (error) {\n            console.error(\"Logout API failed:\", error);\n        } finally{\n            (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.clearAuthTokens)();\n            setUser(null);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(\"已退出登录\");\n        }\n    };\n    // 更新用户资料\n    const updateProfile = async (data)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authAPI.updateProfile(data);\n            if (response.user) {\n                setUser(response.user);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.message || \"资料更新成功\");\n                return true;\n            }\n            return false;\n        } catch (error) {\n            console.error(\"Profile update failed:\", error);\n            const message = error.response?.data?.message || \"资料更新失败\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(message);\n            return false;\n        }\n    };\n    // 修改密码\n    const changePassword = async (data)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.authAPI.changePassword(data);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success(response.message || \"密码修改成功\");\n            return true;\n        } catch (error) {\n            console.error(\"Password change failed:\", error);\n            const message = error.response?.data?.message || \"密码修改失败\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(message);\n            return false;\n        }\n    };\n    // 组件挂载时检查认证状态\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkAuth();\n    }, []);\n    const value = {\n        user,\n        loading,\n        login,\n        register,\n        logout,\n        updateProfile,\n        changePassword\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 257,\n        columnNumber: 10\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authAPI: () => (/* binding */ authAPI),\n/* harmony export */   clearAuthTokens: () => (/* binding */ clearAuthTokens),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getAccessToken: () => (/* binding */ getAccessToken),\n/* harmony export */   getRefreshToken: () => (/* binding */ getRefreshToken),\n/* harmony export */   setAuthTokens: () => (/* binding */ setAuthTokens)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n\n\n// 创建 axios 实例\nconst api = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL:  false ? 0 : \"http://127.0.0.1:8000/api\",\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// 请求拦截器 - 添加认证 token\napi.interceptors.request.use((config)=>{\n    const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器 - 处理错误和 token 刷新\napi.interceptors.response.use((response)=>{\n    return response;\n}, async (error)=>{\n    const originalRequest = error.config;\n    // 如果是 401 错误且不是刷新 token 的请求\n    if (error.response?.status === 401 && !originalRequest._retry) {\n        originalRequest._retry = true;\n        try {\n            const refreshToken = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"refresh_token\");\n            if (refreshToken) {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(`${api.defaults.baseURL}/auth/token/refresh/`, {\n                    refresh: refreshToken\n                });\n                const { access } = response.data;\n                js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"access_token\", access, {\n                    expires: 1\n                }) // 1天过期\n                ;\n                // 重新发送原请求\n                originalRequest.headers.Authorization = `Bearer ${access}`;\n                return api(originalRequest);\n            }\n        } catch (refreshError) {\n            // 刷新失败，清除所有 token 并跳转到登录页\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"access_token\");\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"refresh_token\");\n            window.location.href = \"/auth/login\";\n            return Promise.reject(refreshError);\n        }\n    }\n    // 显示错误消息 - 不在这里显示，让组件自己处理\n    // 这样可以提供更详细的错误信息\n    return Promise.reject(error);\n});\n// 用户认证相关 API\nconst authAPI = {\n    // 用户注册\n    register: async (data)=>{\n        const response = await api.post(\"/auth/register/\", data);\n        return response.data;\n    },\n    // 用户登录\n    login: async (data)=>{\n        const response = await api.post(\"/auth/login/\", data);\n        return response.data;\n    },\n    // 获取用户资料\n    getProfile: async ()=>{\n        const response = await api.get(\"/auth/profile/\");\n        return response.data;\n    },\n    // 更新用户资料\n    updateProfile: async (data)=>{\n        const response = await api.put(\"/auth/profile/\", data);\n        return response.data;\n    },\n    // 修改密码\n    changePassword: async (data)=>{\n        const response = await api.post(\"/auth/password/change/\", data);\n        return response.data;\n    },\n    // 用户登出\n    logout: async ()=>{\n        const response = await api.post(\"/auth/logout/\");\n        return response.data;\n    },\n    // 验证 token\n    verifyToken: async ()=>{\n        const response = await api.get(\"/auth/token/verify/\");\n        return response.data;\n    }\n};\n// 工具函数\nconst setAuthTokens = (tokens)=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"access_token\", tokens.access, {\n        expires: 1\n    }) // 1天过期\n    ;\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(\"refresh_token\", tokens.refresh, {\n        expires: 7\n    }) // 7天过期\n    ;\n};\nconst clearAuthTokens = ()=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"access_token\");\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(\"refresh_token\");\n};\nconst getAccessToken = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"access_token\");\n};\nconst getRefreshToken = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"refresh_token\");\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/bookApi.ts":
/*!****************************!*\
  !*** ./src/lib/bookApi.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bookAPI: () => (/* binding */ bookAPI)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"(ssr)/./src/lib/api.ts\");\n\n// 书籍相关 API\nconst bookAPI = {\n    // 上传书籍\n    uploadBook: async (formData, onUploadProgress)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/books/upload/\", formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            },\n            onUploadProgress: (progressEvent)=>{\n                if (onUploadProgress && progressEvent.total) {\n                    const progress = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n                    onUploadProgress(progress);\n                }\n            }\n        });\n        return response.data;\n    },\n    // 获取书籍列表\n    getBooks: async ()=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/books/\");\n        return response.data;\n    },\n    // 获取书籍详情\n    getBookDetail: async (bookId)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/books/${bookId}/`);\n        return response.data;\n    },\n    // 删除书籍\n    deleteBook: async (bookId)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(`/books/${bookId}/delete/`);\n        return response.data;\n    },\n    // 获取章节内容\n    getChapterContent: async (bookId, chapterId)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/books/${bookId}/chapters/${chapterId}/`);\n        return response.data;\n    },\n    // 获取书籍统计\n    getBookStats: async (bookId)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/books/${bookId}/stats/`);\n        return response.data;\n    },\n    // 更新阅读进度\n    updateReadingProgress: async (bookId, chapterId, position = 0)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/books/${bookId}/progress/`, {\n            chapter_id: chapterId,\n            position: position\n        });\n        return response.data;\n    },\n    // 获取阅读进度\n    getReadingProgress: async (bookId)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/books/${bookId}/progress/get/`);\n        return response.data;\n    },\n    // 获取阅读器设置\n    getReaderSettings: async ()=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/books/reader-settings/\");\n        return response.data;\n    },\n    // 更新阅读器设置\n    updateReaderSettings: async (settings)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/books/reader-settings/\", settings);\n        return response.data;\n    },\n    // 重置阅读器设置\n    resetReaderSettings: async ()=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/books/reader-settings/\");\n        return response.data;\n    },\n    // 书签管理\n    // 获取书签列表\n    getBookmarks: async (bookId, page = 1, pageSize = 20)=>{\n        const url = bookId ? `/books/${bookId}/bookmarks/` : \"/books/bookmarks/\";\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n            params: {\n                page,\n                page_size: pageSize\n            }\n        });\n        return response.data;\n    },\n    // 创建书签\n    createBookmark: async (bookId, bookmarkData)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/books/${bookId}/bookmarks/`, bookmarkData);\n        return response.data;\n    },\n    // 获取书签详情\n    getBookmarkDetail: async (bookmarkId)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/books/bookmarks/${bookmarkId}/`);\n        return response.data;\n    },\n    // 更新书签\n    updateBookmark: async (bookmarkId, bookmarkData)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`/books/bookmarks/${bookmarkId}/update/`, bookmarkData);\n        return response.data;\n    },\n    // 删除书签\n    deleteBookmark: async (bookmarkId)=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(`/books/bookmarks/${bookmarkId}/delete/`);\n        return response.data;\n    },\n    // 生成章节总结\n    generateChapterSummary: async (bookId, chapterId, language = \"zh-CN\")=>{\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`/books/${bookId}/chapters/${chapterId}/summary/`, {\n            language: language\n        });\n        return response.data;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/bookApi.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"179f011296ba\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYm9va3JlYWxtLWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz9jYmYzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMTc5ZjAxMTI5NmJhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/reader-themes.css":
/*!**************************************!*\
  !*** ./src/styles/reader-themes.css ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ce510e795c69\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL3JlYWRlci10aGVtZXMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYm9va3JlYWxtLWZyb250ZW5kLy4vc3JjL3N0eWxlcy9yZWFkZXItdGhlbWVzLmNzcz8xYjc4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiY2U1MTBlNzk1YzY5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/reader-themes.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/books/page.tsx":
/*!******************************************!*\
  !*** ./src/app/dashboard/books/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\augment-test\util\BookRealm\frontend\src\app\dashboard\books\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _styles_reader_themes_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/reader-themes.css */ \"(rsc)/./src/styles/reader-themes.css\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"BookRealm - 电子书阅读平台\",\n    description: \"智能电子书阅读平台，支持TXT文件解析、AI翻译等功能\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.AuthProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e1),
/* harmony export */   useAuth: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\augment-test\util\BookRealm\frontend\src\contexts\AuthContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\augment-test\util\BookRealm\frontend\src\contexts\AuthContext.tsx#useAuth`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\augment-test\util\BookRealm\frontend\src\contexts\AuthContext.tsx#AuthProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/axios","vendor-chunks/mime-db","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/react-hot-toast","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/js-cookie","vendor-chunks/proxy-from-env","vendor-chunks/goober","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/es-define-property","vendor-chunks/gopd","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fbooks%2Fpage&page=%2Fdashboard%2Fbooks%2Fpage&appPaths=%2Fdashboard%2Fbooks%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fbooks%2Fpage.tsx&appDir=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();