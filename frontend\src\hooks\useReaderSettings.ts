import { useState, useEffect, useCallback } from 'react'
import { bookAPI, ReaderSettings } from '@/lib/bookApi'
import toast from 'react-hot-toast'

// 默认设置
const DEFAULT_SETTINGS: Partial<ReaderSettings> = {
  font_size: 16,
  line_height: 1.6,
  font_family: 'system',
  theme: 'light',
  page_width: 800,
  text_align: 'left',
  auto_scroll: false,
  scroll_speed: 50,
  show_progress: true,
  full_screen: false,
}

export const useReaderSettings = () => {
  const [settings, setSettings] = useState<ReaderSettings | null>(null)
  const [tempSettings, setTempSettings] = useState<ReaderSettings | null>(null) // 临时设置
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  // 获取设置
  const fetchSettings = useCallback(async () => {
    try {
      setLoading(true)
      const response = await bookAPI.getReaderSettings()
      if (response.success) {
        setSettings(response.data)
        setTempSettings(response.data) // 同时设置临时设置
      } else {
        // 如果获取失败，使用默认设置
        const defaultSettings = DEFAULT_SETTINGS as ReaderSettings
        setSettings(defaultSettings)
        setTempSettings(defaultSettings)
      }
    } catch (error) {
      console.error('Failed to fetch reader settings:', error)
      // 使用默认设置
      const defaultSettings = DEFAULT_SETTINGS as ReaderSettings
      setSettings(defaultSettings)
      setTempSettings(defaultSettings)
    } finally {
      setLoading(false)
    }
  }, [])

  // 更新设置
  const updateSettings = useCallback(async (newSettings: Partial<ReaderSettings>, options?: { silent?: boolean }) => {
    try {
      setSaving(true)
      const response = await bookAPI.updateReaderSettings(newSettings)
      if (response.success) {
        setSettings(response.data)
        if (!options?.silent) {
          toast.success('设置已保存')
        }
        return true
      } else {
        if (!options?.silent) {
          toast.error(response.message || '保存设置失败')
        }
        return false
      }
    } catch (error) {
      console.error('Failed to update reader settings:', error)
      if (!options?.silent) {
        toast.error('保存设置失败')
      }
      return false
    } finally {
      setSaving(false)
    }
  }, [])

  // 重置设置
  const resetSettings = useCallback(async () => {
    try {
      setSaving(true)
      const response = await bookAPI.resetReaderSettings()
      if (response.success) {
        setSettings(response.data)
        toast.success('设置已重置为默认值')
        return true
      } else {
        toast.error(response.message || '重置设置失败')
        return false
      }
    } catch (error) {
      console.error('Failed to reset reader settings:', error)
      toast.error('重置设置失败')
      return false
    } finally {
      setSaving(false)
    }
  }, [])

  // 临时更新设置项（不保存到服务器）
  const updateTempSetting = useCallback(<K extends keyof ReaderSettings>(
    key: K,
    value: ReaderSettings[K]
  ) => {
    if (!tempSettings) return

    const newTempSettings = { ...tempSettings, [key]: value }
    setTempSettings(newTempSettings)
  }, [tempSettings])

  // 保存临时设置到服务器
  const saveTempSettings = useCallback(async (options?: { silent?: boolean }) => {
    if (!tempSettings) return false

    const result = await updateSettings(tempSettings, options)
    if (result) {
      setSettings(tempSettings) // 保存成功后更新正式设置
      // 延迟触发主题应用，确保状态已更新
      setTimeout(() => {
        // 手动触发主题应用
        const currentSettings = tempSettings
        if (!currentSettings) return

        const root = document.documentElement

        // 清除所有现有的阅读器相关 CSS 变量
        const readerVars = [
          '--reader-bg', '--reader-text', '--reader-border', '--reader-secondary',
          '--reader-font-size', '--reader-line-height', '--reader-font-family',
          '--reader-page-width', '--reader-text-align'
        ]

        readerVars.forEach(varName => {
          root.style.removeProperty(varName)
        })

        // 重新计算样式
        const { theme, font_size, line_height, font_family, page_width, text_align } = currentSettings

        const themeColors = {
          light: {
            '--reader-bg': '#ffffff',
            '--reader-text': '#1f2937',
            '--reader-border': '#e5e7eb',
            '--reader-secondary': '#6b7280',
          },
          dark: {
            '--reader-bg': '#1f2937',
            '--reader-text': '#f9fafb',
            '--reader-border': '#374151',
            '--reader-secondary': '#9ca3af',
          },
          sepia: {
            '--reader-bg': '#f7f3e9',
            '--reader-text': '#5c4a37',
            '--reader-border': '#e6dcc6',
            '--reader-secondary': '#8b7355',
          },
          night: {
            '--reader-bg': '#0f172a',
            '--reader-text': '#e2e8f0',
            '--reader-border': '#1e293b',
            '--reader-secondary': '#64748b',
          },
        }

        const fontFamilies = {
          system: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
          serif: 'Georgia, "Times New Roman", serif',
          'sans-serif': 'Arial, Helvetica, sans-serif',
          monospace: '"Courier New", Consolas, monospace',
        }

        const styles = {
          ...themeColors[theme],
          '--reader-font-size': `${font_size}px`,
          '--reader-line-height': line_height,
          '--reader-font-family': fontFamilies[font_family],
          '--reader-page-width': `${page_width}px`,
          '--reader-text-align': text_align,
        }

        // 重新设置所有样式变量
        Object.entries(styles).forEach(([key, value]) => {
          root.style.setProperty(key, value as string)
        })

        // 清除旧的主题类名并添加新的
        document.body.className = document.body.className
          .replace(/theme-\w+/g, '')
          .trim()
        document.body.classList.add(`theme-${currentSettings.theme}`)

        // 强制重绘和重新计算样式
        document.body.offsetHeight

        // 触发自定义事件通知样式已更新
        window.dispatchEvent(new CustomEvent('readerThemeChanged', {
          detail: { theme: currentSettings.theme, settings: currentSettings }
        }))
      }, 100)
    }
    return result
  }, [tempSettings, updateSettings])

  // 取消临时设置（恢复到保存的设置）
  const cancelTempSettings = useCallback(() => {
    if (settings) {
      setTempSettings({ ...settings })
    }
  }, [settings])

  // 获取主题相关的 CSS 变量（使用临时设置）
  const getThemeStyles = useCallback(() => {
    const currentSettings = tempSettings || settings
    if (!currentSettings) return {}

    const { theme, font_size, line_height, font_family, page_width, text_align } = currentSettings

    const themeColors = {
      light: {
        '--reader-bg': '#ffffff',
        '--reader-text': '#1f2937',
        '--reader-border': '#e5e7eb',
        '--reader-secondary': '#6b7280',
      },
      dark: {
        '--reader-bg': '#1f2937',
        '--reader-text': '#f9fafb',
        '--reader-border': '#374151',
        '--reader-secondary': '#9ca3af',
      },
      sepia: {
        '--reader-bg': '#f7f3e9',
        '--reader-text': '#5c4a37',
        '--reader-border': '#e6dcc6',
        '--reader-secondary': '#8b7355',
      },
      night: {
        '--reader-bg': '#0f172a',
        '--reader-text': '#e2e8f0',
        '--reader-border': '#1e293b',
        '--reader-secondary': '#64748b',
      },
    }

    const fontFamilies = {
      system: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      serif: 'Georgia, "Times New Roman", serif',
      'sans-serif': 'Arial, Helvetica, sans-serif',
      monospace: '"Courier New", Consolas, monospace',
    }

    return {
      ...themeColors[theme],
      '--reader-font-size': `${font_size}px`,
      '--reader-line-height': line_height,
      '--reader-font-family': fontFamilies[font_family],
      '--reader-page-width': `${page_width}px`,
      '--reader-text-align': text_align,
    } as React.CSSProperties
  }, [settings])

  // 应用主题到 body（使用临时设置）
  const applyTheme = useCallback(() => {
    const currentSettings = tempSettings || settings
    if (!currentSettings) return

    const styles = getThemeStyles()
    const root = document.documentElement

    // 清除所有现有的阅读器相关 CSS 变量
    const readerVars = [
      '--reader-bg', '--reader-text', '--reader-border', '--reader-secondary',
      '--reader-font-size', '--reader-line-height', '--reader-font-family',
      '--reader-page-width', '--reader-text-align'
    ]

    readerVars.forEach(varName => {
      root.style.removeProperty(varName)
    })

    // 重新设置所有样式变量
    Object.entries(styles).forEach(([key, value]) => {
      root.style.setProperty(key, value as string)
    })

    // 清除旧的主题类名并添加新的
    document.body.className = document.body.className
      .replace(/theme-\w+/g, '')
      .trim()
    document.body.classList.add(`theme-${currentSettings.theme}`)

    // 强制重绘和重新计算样式
    document.body.offsetHeight

    // 触发自定义事件通知样式已更新
    window.dispatchEvent(new CustomEvent('readerThemeChanged', {
      detail: { theme: currentSettings.theme, settings: currentSettings }
    }))
  }, [tempSettings, settings, getThemeStyles])

  // 初始化
  useEffect(() => {
    fetchSettings()
  }, [fetchSettings])

  // 应用主题（监听临时设置变化）
  useEffect(() => {
    if (tempSettings || settings) {
      applyTheme()
    }
  }, [tempSettings, settings, applyTheme])

  return {
    settings,
    tempSettings,
    loading,
    saving,
    updateSettings,
    updateTempSetting,
    saveTempSettings,
    cancelTempSettings,
    resetSettings,
    getThemeStyles,
    applyTheme,
    fetchSettings,
  }
}
