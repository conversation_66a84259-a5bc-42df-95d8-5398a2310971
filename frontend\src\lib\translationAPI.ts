import axios from 'axios';
import Cookies from 'js-cookie';

// 创建axios实例
const api = axios.create({
  baseURL: process.env.NODE_ENV === 'production' 
    ? 'https://your-api-domain.com/api' 
    : 'http://127.0.0.1:8000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token - 使用Cookies而不是localStorage
    const token = Cookies.get('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 处理401未授权错误
    if (error.response?.status === 401) {
      // 检查是否是翻译相关API的请求
      const isTranslationAPI = error.config?.url?.includes('/books/translate');
      const isTranslationSettings = error.config?.url?.includes('/translation/settings/');

      if (!isTranslationAPI && !isTranslationSettings) {
        // 非翻译相关API的401错误才跳转登录页面
        Cookies.remove('access_token');
        Cookies.remove('refresh_token');
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      }
    }
    return Promise.reject(error);
  }
);

/**
 * 翻译相关API服务
 */
export const translationAPI = {
  /**
   * 翻译文本
   */
  async translate(params: {
    text: string;
    target_language?: string;
    source_language?: string;
    context?: string;
    book_id?: string;
    chapter_id?: string;
    position?: number;
  }) {
    try {
      const response = await api.post('/books/translate/', params);
      return response.data;
    } catch (error) {
      console.error('Translation API error:', error);
      throw error;
    }
  },

  /**
   * 获取翻译历史
   */
  async getHistory(params: {
    page?: number;
    page_size?: number;
    book_id?: string;
    is_favorited?: string;
    source_language?: string;
    target_language?: string;
    search?: string;
  } = {}) {
    try {
      const response = await api.get('/books/translation/history/', { params });
      return response.data;
    } catch (error) {
      console.error('Get translation history error:', error);
      throw error;
    }
  },

  /**
   * 删除翻译历史
   */
  async deleteHistory(translationIds: string[]) {
    try {
      const response = await api.delete('/books/translation/history/', {
        data: { translation_ids: translationIds }
      });
      return response.data;
    } catch (error) {
      console.error('Delete translation history error:', error);
      throw error;
    }
  },

  /**
   * 切换翻译收藏状态
   */
  async toggleFavorite(translationId: string) {
    try {
      const response = await api.put(`/books/translation/${translationId}/favorite/`);
      return response.data;
    } catch (error) {
      console.error('Toggle translation favorite error:', error);
      throw error;
    }
  },

  /**
   * 获取翻译设置
   */
  async getSettings() {
    try {
      const response = await api.get('/books/translation/settings/');
      return response.data;
    } catch (error) {
      console.error('Get translation settings error:', error);
      throw error;
    }
  },

  /**
   * 更新翻译设置
   */
  async updateSettings(settings: any) {
    try {
      const response = await api.put('/books/translation/settings/', settings);
      return response.data;
    } catch (error) {
      console.error('Update translation settings error:', error);
      throw error;
    }
  },

  /**
   * 获取翻译统计信息
   */
  async getStats() {
    try {
      const response = await api.get('/books/translation/stats/');
      return response.data;
    } catch (error) {
      console.error('Get translation stats error:', error);
      throw error;
    }
  },

  /**
   * 批量翻译
   */
  async batchTranslate(texts: Array<{
    text: string;
    context?: string;
    book_id?: string;
    chapter_id?: string;
    position?: number;
  }>, targetLanguage = 'zh-CN', sourceLanguage = 'auto') {
    try {
      const promises = texts.map(textObj => 
        this.translate({
          text: textObj.text,
          target_language: targetLanguage,
          source_language: sourceLanguage,
          context: textObj.context || '',
          book_id: textObj.book_id,
          chapter_id: textObj.chapter_id,
          position: textObj.position
        })
      );

      const results = await Promise.allSettled(promises);
      
      return results.map((result, index) => ({
        original: texts[index],
        success: result.status === 'fulfilled',
        data: result.status === 'fulfilled' ? result.value : null,
        error: result.status === 'rejected' ? result.reason : null
      }));
    } catch (error) {
      console.error('Batch translate error:', error);
      throw error;
    }
  },

  /**
   * 检测文本语言
   */
  detectLanguage(text: string): string {
    if (!text || typeof text !== 'string') return 'auto';
    
    // 简单的客户端语言检测
    const chineseRegex = /[\u4e00-\u9fff]/g;
    const englishRegex = /[a-zA-Z]/g;
    const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/g;
    const koreanRegex = /[\uac00-\ud7af]/g;
    
    const chineseMatches = text.match(chineseRegex) || [];
    const englishMatches = text.match(englishRegex) || [];
    const japaneseMatches = text.match(japaneseRegex) || [];
    const koreanMatches = text.match(koreanRegex) || [];
    
    const totalLength = text.length;
    
    const chineseRatio = chineseMatches.length / totalLength;
    const englishRatio = englishMatches.length / totalLength;
    const japaneseRatio = japaneseMatches.length / totalLength;
    const koreanRatio = koreanMatches.length / totalLength;
    
    // 根据字符占比判断语言
    if (chineseRatio > 0.3) return 'zh-CN';
    if (englishRatio > 0.5) return 'en';
    if (japaneseRatio > 0.3) return 'ja';
    if (koreanRatio > 0.3) return 'ko';
    
    return 'auto';
  },

  /**
   * 获取推荐的目标语言
   */
  getRecommendedTargetLanguage(sourceLanguage: string): string {
    const recommendations: Record<string, string> = {
      'zh-CN': 'en',
      'en': 'zh-CN',
      'ja': 'zh-CN',
      'ko': 'zh-CN',
      'fr': 'en',
      'de': 'en',
      'es': 'en',
      'auto': 'zh-CN'
    };
    
    return recommendations[sourceLanguage] || 'zh-CN';
  },

  /**
   * 格式化语言显示名称
   */
  getLanguageName(languageCode: string): string {
    const languageNames: Record<string, string> = {
      'auto': '自动检测',
      'zh-CN': '中文',
      'en': 'English',
      'ja': '日本語',
      'ko': '한국어',
      'fr': 'Français',
      'de': 'Deutsch',
      'es': 'Español',
      'ru': 'Русский',
      'it': 'Italiano',
      'pt': 'Português'
    };
    
    return languageNames[languageCode] || languageCode;
  },

  /**
   * 验证翻译文本
   */
  validateText(text: string): { valid: boolean; error?: string; text?: string } {
    if (!text || typeof text !== 'string') {
      return { valid: false, error: '文本不能为空' };
    }
    
    const trimmedText = text.trim();
    
    if (trimmedText.length < 1) {
      return { valid: false, error: '文本不能为空' };
    }
    
    if (trimmedText.length > 5000) {
      return { valid: false, error: '文本长度不能超过5000字符' };
    }
    
    return { valid: true, text: trimmedText };
  }
};

export default translationAPI;
