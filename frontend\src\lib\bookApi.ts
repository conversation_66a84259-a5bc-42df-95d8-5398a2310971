import api from './api'

// 书籍相关 API
export const bookAPI = {
  // 上传书籍
  uploadBook: async (formData: FormData, onUploadProgress?: (progress: number) => void) => {
    const response = await api.post('/books/upload/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onUploadProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onUploadProgress(progress)
        }
      },
    })
    return response.data
  },

  // 获取书籍列表
  getBooks: async () => {
    const response = await api.get('/books/')
    return response.data
  },

  // 获取书籍详情
  getBookDetail: async (bookId: string) => {
    const response = await api.get(`/books/${bookId}/`)
    return response.data
  },

  // 删除书籍
  deleteBook: async (bookId: string) => {
    const response = await api.delete(`/books/${bookId}/delete/`)
    return response.data
  },

  // 获取章节内容
  getChapterContent: async (bookId: string, chapterId: string) => {
    const response = await api.get(`/books/${bookId}/chapters/${chapterId}/`)
    return response.data
  },

  // 获取书籍统计
  getBookStats: async (bookId: string) => {
    const response = await api.get(`/books/${bookId}/stats/`)
    return response.data
  },

  // 更新阅读进度
  updateReadingProgress: async (bookId: string, chapterId: string, position: number = 0) => {
    const response = await api.post(`/books/${bookId}/progress/`, {
      chapter_id: chapterId,
      position: position
    })
    return response.data
  },

  // 获取阅读进度
  getReadingProgress: async (bookId: string) => {
    const response = await api.get(`/books/${bookId}/progress/get/`)
    return response.data
  },

  // 获取阅读器设置
  getReaderSettings: async () => {
    const response = await api.get('/books/reader-settings/')
    return response.data
  },

  // 更新阅读器设置
  updateReaderSettings: async (settings: Partial<ReaderSettings>) => {
    const response = await api.put('/books/reader-settings/', settings)
    return response.data
  },

  // 重置阅读器设置
  resetReaderSettings: async () => {
    const response = await api.post('/books/reader-settings/')
    return response.data
  },

  // 书签管理
  // 获取书签列表
  getBookmarks: async (bookId?: string, page: number = 1, pageSize: number = 20) => {
    const url = bookId ? `/books/${bookId}/bookmarks/` : '/books/bookmarks/'
    const response = await api.get(url, {
      params: { page, page_size: pageSize }
    })
    return response.data
  },

  // 创建书签
  createBookmark: async (bookId: string, bookmarkData: CreateBookmarkData) => {
    const response = await api.post(`/books/${bookId}/bookmarks/`, bookmarkData)
    return response.data
  },

  // 获取书签详情
  getBookmarkDetail: async (bookmarkId: string) => {
    const response = await api.get(`/books/bookmarks/${bookmarkId}/`)
    return response.data
  },

  // 更新书签
  updateBookmark: async (bookmarkId: string, bookmarkData: Partial<CreateBookmarkData>) => {
    const response = await api.put(`/books/bookmarks/${bookmarkId}/update/`, bookmarkData)
    return response.data
  },

  // 删除书签
  deleteBookmark: async (bookmarkId: string) => {
    const response = await api.delete(`/books/bookmarks/${bookmarkId}/delete/`)
    return response.data
  },

  // 生成章节总结
  generateChapterSummary: async (bookId: string, chapterId: string, language: string = 'zh-CN') => {
    const response = await api.post(`/books/${bookId}/chapters/${chapterId}/summary/`, {
      language: language
    })
    return response.data
  },
}

// 书籍类型定义
export interface Book {
  id: string
  title: string
  author: string
  filename: string
  status: 'uploading' | 'parsing' | 'ready' | 'error'
  total_chapters: number
  total_words: number
  file_size: number
  uploaded_at: string
  updated_at: string
}

export interface Chapter {
  id: string
  title: string
  order_index: number
  content_preview: string
  word_count: number
  created_at: string
  updated_at: string
}

export interface BookDetail extends Book {
  chapters: Chapter[]
  reading_progress?: {
    current_chapter: string | null
    current_position: number
    progress_percentage: number
    last_read_at: string
  }
}

export interface UploadResponse {
  success: boolean
  message: string
  data?: {
    book_id: string
    title: string
    author: string
    total_chapters: number
    total_words: number
    file_size: number
  }
  errors?: any
}

export interface ChapterContent {
  id: string
  title: string
  content: string
  order_index: number
  word_count: number
  book_title: string
  book_id: string
}

export interface ReadingProgress {
  current_chapter_id: string | null
  current_chapter_index: number
  current_position: number
  progress_percentage: number
  last_read_at: string | null
  has_progress: boolean
}

export interface BookStats {
  reading_progress: ReadingProgress | null
  bookmark_count: number
  total_chapters: number
  total_words: number
  file_size: number
}

export interface ReaderSettings {
  user: string
  font_size: number
  line_height: number
  font_family: 'system' | 'serif' | 'sans-serif' | 'monospace'
  theme: 'light' | 'dark' | 'sepia' | 'night'
  page_width: number
  text_align: 'left' | 'justify' | 'center'
  auto_scroll: boolean
  scroll_speed: number
  show_progress: boolean
  full_screen: boolean
  created_at: string
  updated_at: string
}

export interface Bookmark {
  id: string
  user: string
  book: string
  chapter: string
  position: number
  scroll_position: number
  title: string
  note: string
  type: 'manual' | 'auto_highlight' | 'note'
  color: 'blue' | 'red' | 'green' | 'yellow' | 'purple' | 'orange'
  is_private: boolean
  context_before: string
  context_after: string
  context_preview: string
  chapter_title: string
  chapter_order: number
  book_title: string
  created_at: string
  updated_at: string
}

export interface CreateBookmarkData {
  chapter_id: string
  position: number
  scroll_position?: number
  title?: string
  note?: string
  type?: 'manual' | 'auto_highlight' | 'note'
  color?: 'blue' | 'red' | 'green' | 'yellow' | 'purple' | 'orange'
  is_private?: boolean
}

export interface BookmarkListResponse {
  bookmarks: Bookmark[]
  pagination: {
    current_page: number
    total_pages: number
    total_count: number
    has_next: boolean
    has_previous: boolean
  }
}
