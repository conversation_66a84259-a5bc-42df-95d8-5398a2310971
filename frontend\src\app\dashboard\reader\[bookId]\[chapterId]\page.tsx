'use client'

import { useState, useEffect, useRef } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { bookAPI, ChapterContent } from '@/lib/bookApi'
import { useReadingProgress } from '@/hooks/useReadingProgress'
import { useReaderSettings } from '@/hooks/useReaderSettings'
import { useBookmarks } from '@/hooks/useBookmarks'
import ReaderSettingsPanel from '@/components/ReaderSettingsPanel'
import BookmarkButton from '@/components/BookmarkButton'
import BookmarkList from '@/components/BookmarkList'
import { TranslationPopup, TranslationHistory, TranslationSettings, ImmersiveTranslation } from '@/components/Translation'
import ChapterSummary from '@/components/ChapterSummary'
import useTextSelection from '@/hooks/useTextSelection'
import { 
  ArrowLeft, BookOpen, ChevronLeft, ChevronRight, Clock, Save, 
  Settings, Bookmark, Languages, History, Menu, X, Maximize2, 
  Minimize2, Eye, EyeOff, Volume2, Pause, Play, Moon, Sun,
  FileText, MoreVertical, Home, List, Search, Type, Palette,
  ZoomIn, ZoomOut, RotateCcw, Sidebar, Coffee, MousePointer2
} from 'lucide-react'
import Link from 'next/link'
import toast from 'react-hot-toast'
import '@/styles/resizable-sidebar.css'
import '@/styles/draggable-panel.css'

export default function ReaderPage() {
  const { user } = useAuth()
  const params = useParams()
  const router = useRouter()
  const bookId = params.bookId as string
  const chapterId = params.chapterId as string

  const [chapter, setChapter] = useState<ChapterContent | null>(null)
  const [loading, setLoading] = useState(true)
  const [allChapters, setAllChapters] = useState<any[]>([])
  const [currentChapterIndex, setCurrentChapterIndex] = useState(0)
  const [showSettings, setShowSettings] = useState(false)
  const [showBookmarks, setShowBookmarks] = useState(false)
  const [showTranslationHistory, setShowTranslationHistory] = useState(false)
  const [showTranslationSettings, setShowTranslationSettings] = useState(false)
  const [showImmersiveTranslation, setShowImmersiveTranslation] = useState(false)
  const [translationSettings, setTranslationSettings] = useState<any>(null)
  const [showChapterSummary, setShowChapterSummary] = useState(false)
  const [forceUpdate, setForceUpdate] = useState(0)
  
  // 新增UI控制状态
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showToolbar, setShowToolbar] = useState(true)
  const [showChapterList, setShowChapterList] = useState(false)
  const [autoHideToolbar, setAutoHideToolbar] = useState(false)
  const [toolbarTimeout, setToolbarTimeout] = useState<NodeJS.Timeout | null>(null)
  
  // 增强的阅读体验状态
  const [readingMode, setReadingMode] = useState<'normal' | 'focus' | 'immersive'>('normal')
  const [showReadingStats, setShowReadingStats] = useState(false)
  const [autoScroll, setAutoScroll] = useState(false)
  const [scrollSpeed, setScrollSpeed] = useState(50)
  const [showQuickActions, setShowQuickActions] = useState(true)
  const [wordHighlight, setWordHighlight] = useState(false)
  const [lineHighlight, setLineHighlight] = useState(false)
  const [showKeyboardHelp, setShowKeyboardHelp] = useState(false)
  const [sidebarWidth, setSidebarWidth] = useState(320) // 默认320px
  const [isResizing, setIsResizing] = useState(false)

  // 拖动相关状态
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [panelPosition, setPanelPosition] = useState({ x: 20, y: 100 }) // 默认位置

  const contentRef = useRef<HTMLDivElement>(null)
  const sidebarRef = useRef<HTMLDivElement>(null)
  const resizerRef = useRef<HTMLDivElement>(null)

  // 使用文本选择Hook进行划词翻译
  const {
    showTranslation,
    selectedText,
    selectionPosition,
    hideTranslation,
    clearSelection,
    getTextPosition
  } = useTextSelection(contentRef, {
    minLength: 2,
    maxLength: 1000,
    enableTranslation: true
  })

  // 使用阅读进度管理 Hook
  const {
    progress,
    saving,
    totalReadingTime,
    updateProgress,
    startReading,
    stopReading,
    getFormattedReadingTime
  } = useReadingProgress({
    bookId,
    chapterId,
    autoSave: true,
    saveInterval: 3000 // 3秒自动保存
  })

  // 使用阅读器设置 Hook
  const { settings, tempSettings, getThemeStyles, updateTempSetting, saveTempSettings } = useReaderSettings()

  // 使用当前有效的设置（临时设置优先）
  const currentSettings = tempSettings || settings

  // 使用书签 Hook
  const { findBookmarkByPosition } = useBookmarks({ bookId })



  // 加载翻译设置
  useEffect(() => {
    const loadTranslationSettings = async () => {
      try {
        const { translationAPI } = await import('@/lib/translationAPI')
        const response = await translationAPI.getSettings()
        console.log('翻译设置加载响应:', response);
        if (response.success || response.default_target_language) {
          // 处理两种可能的响应格式
          const settings = response.data || response;
          setTranslationSettings(settings)
          console.log('翻译设置已加载:', settings);
        } else {
          // 设置默认值
          const defaultSettings = {
            default_source_language: 'auto',
            default_target_language: 'zh-CN',
            immersive_enabled: true,
            immersive_auto_translate: false,
            immersive_split_ratio: 0.5
          }
          setTranslationSettings(defaultSettings)
          console.log('使用默认翻译设置:', defaultSettings);
        }
      } catch (error) {
        console.error('Failed to load translation settings:', error)
        // 设置默认值
        const defaultSettings = {
          default_source_language: 'auto',
          default_target_language: 'zh-CN',
          immersive_enabled: true,
          immersive_auto_translate: false,
          immersive_split_ratio: 0.5
        }
        setTranslationSettings(defaultSettings)
        console.log('加载失败，使用默认翻译设置:', defaultSettings);
      }
    }
    loadTranslationSettings()
  }, [])

  // 切换沉浸式翻译
  const toggleImmersiveTranslation = () => {
    console.log('切换沉浸式翻译:', !showImmersiveTranslation);
    console.log('当前翻译设置:', translationSettings);
    console.log('当前章节内容:', chapter?.content?.substring(0, 100) + '...');
    setShowImmersiveTranslation(!showImmersiveTranslation)
  }

  // 侧边栏拖拽调整大小功能
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    setIsResizing(true)
    document.addEventListener('mousemove', handleSidebarMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
    document.body.classList.add('resizing')
  }

  const handleSidebarMouseMove = (e: MouseEvent) => {
    if (!isResizing) return

    const newWidth = Math.max(240, Math.min(600, e.clientX))
    setSidebarWidth(newWidth)
  }

  const handleMouseUp = () => {
    setIsResizing(false)
    document.removeEventListener('mousemove', handleSidebarMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    document.body.classList.remove('resizing')
  }

  // 双击重置侧边栏宽度
  const handleDoubleClick = () => {
    setSidebarWidth(320) // 重置为默认宽度
  }

  // 拖动面板相关函数
  const handlePanelMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)

    const rect = (e.currentTarget.parentElement as HTMLElement).getBoundingClientRect()
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    })

    document.addEventListener('mousemove', handlePanelMouseMove)
    document.addEventListener('mouseup', handlePanelMouseUp)
    document.body.classList.add('dragging')
  }

  const handlePanelMouseMove = (e: MouseEvent) => {
    if (!isDragging) return

    const newX = Math.max(0, Math.min(window.innerWidth - 320, e.clientX - dragOffset.x))
    const newY = Math.max(0, Math.min(window.innerHeight - 400, e.clientY - dragOffset.y))

    setPanelPosition({ x: newX, y: newY })
  }

  const handlePanelMouseUp = () => {
    setIsDragging(false)
    document.removeEventListener('mousemove', handlePanelMouseMove)
    document.removeEventListener('mouseup', handlePanelMouseUp)
    document.body.classList.remove('dragging')
  }



  // 获取书籍详情和章节列表
  const fetchBookAndChapter = async () => {
    try {
      setLoading(true)

      // 获取书籍详情（包含章节列表）
      const bookResponse = await bookAPI.getBookDetail(bookId)
      if (bookResponse.success && bookResponse.data.chapters) {
        setAllChapters(bookResponse.data.chapters)

        // 找到当前章节的索引
        const chapterIndex = bookResponse.data.chapters.findIndex(
          (ch: any) => ch.id === chapterId
        )
        setCurrentChapterIndex(chapterIndex >= 0 ? chapterIndex : 0)
      }

      // 获取当前章节内容
      const chapterResponse = await bookAPI.getChapterContent(bookId, chapterId)
      if (chapterResponse.success) {
        setChapter(chapterResponse.data)
      }
    } catch (error: any) {
      console.error('Failed to fetch chapter content:', error)
      toast.error('获取章节内容失败')
      router.push(`/dashboard/books/${bookId}`)
    } finally {
      setLoading(false)
    }
  }

  // 切换到指定章节
  const navigateToChapter = (targetChapterIndex: number) => {
    if (targetChapterIndex >= 0 && targetChapterIndex < allChapters.length) {
      const targetChapter = allChapters[targetChapterIndex]
      router.push(`/dashboard/reader/${bookId}/${targetChapter.id}`)
    }
  }

  // 上一章
  const goToPreviousChapter = () => {
    if (currentChapterIndex > 0) {
      navigateToChapter(currentChapterIndex - 1)
    }
  }

  // 下一章
  const goToNextChapter = () => {
    if (currentChapterIndex < allChapters.length - 1) {
      navigateToChapter(currentChapterIndex + 1)
    }
  }

  // 处理书签跳转
  const handleBookmarkJump = async (bookmark: any) => {
    // 如果书签在当前章节，直接滚动到位置
    if (bookmark.chapter === chapterId) {
      if (bookmark.scroll_position > 0) {
        const contentElement = contentRef.current
        if (contentElement) {
          contentElement.scrollTop = bookmark.scroll_position
        }
      }
      setShowBookmarks(false)
      toast.success('已跳转到书签位置')
    } else {
      // 如果书签在其他章节，跳转到对应章节
      router.push(`/dashboard/reader/${bookId}/${bookmark.chapter}`)
      setShowBookmarks(false)
    }
  }

  // 切换全屏模式
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }

  // 工具栏自动隐藏
  const handleMouseMove = () => {
    if (autoHideToolbar) {
      setShowToolbar(true)
      if (toolbarTimeout) {
        clearTimeout(toolbarTimeout)
      }
      const timeout = setTimeout(() => {
        setShowToolbar(false)
      }, 3000)
      setToolbarTimeout(timeout)
    }
  }

  // 键盘快捷键 - 增强版
  const handleKeyPress = (e: KeyboardEvent) => {
    // 如果正在输入框中，不处理快捷键
    if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
      return
    }
    
    // Ctrl/Cmd + 键的组合
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case '=':
        case '+':
          e.preventDefault()
          // 增大字体
          const currentSizeUp = currentSettings?.font_size || 18
          const newSizeUp = Math.min(36, currentSizeUp + 2)
          if (newSizeUp !== currentSizeUp) {
            updateTempSetting('font_size', newSizeUp)
            setTimeout(() => saveTempSettings({ silent: true }), 100)
          }
          break
        case '-':
          e.preventDefault()
          // 减小字体
          const currentSizeDown = currentSettings?.font_size || 18
          const newSizeDown = Math.max(12, currentSizeDown - 2)
          if (newSizeDown !== currentSizeDown) {
            updateTempSetting('font_size', newSizeDown)
            setTimeout(() => saveTempSettings({ silent: true }), 100)
          }
          break
        case '0':
          e.preventDefault()
          // 重置字体大小
          const currentSizeReset = currentSettings?.font_size || 18
          if (currentSizeReset !== 18) {
            updateTempSetting('font_size', 18)
            setTimeout(() => saveTempSettings({ silent: true }), 100)
          }
          break
      }
      return
    }
    
    // 单键快捷键
    switch (e.key) {
      case 'ArrowLeft':
      case 'h':
        e.preventDefault()
        goToPreviousChapter()
        toast.success('已切换到上一章')
        break
      case 'ArrowRight':
      case 'l':
        e.preventDefault()
        goToNextChapter()
        toast.success('已切换到下一章')
        break
      case 'f':
      case 'F':
        e.preventDefault()
        toggleFullscreen()
        break
      case 's':
      case 'S':
        e.preventDefault()
        setShowSettings(true)
        break
      case 'b':
      case 'B':
        e.preventDefault()
        setShowBookmarks(true)
        break
      case 't':
      case 'T':
        e.preventDefault()
        toggleImmersiveTranslation()
        break
      case 'r':
      case 'R':
        e.preventDefault()
        setShowReadingStats(!showReadingStats)
        break
      case 'm':
      case 'M':
        e.preventDefault()
        // 切换阅读模式
        const modes: ('normal' | 'focus' | 'immersive')[] = ['normal', 'focus', 'immersive']
        const currentIndex = modes.indexOf(readingMode)
        const nextMode = modes[(currentIndex + 1) % modes.length]
        setReadingMode(nextMode)
        toast.success(`已切换到${nextMode === 'normal' ? '标准' : nextMode === 'focus' ? '专注' : '沉浸'}模式`)
        break
      case 'c':
      case 'C':
        e.preventDefault()
        setShowChapterList(!showChapterList)
        break
      case 'q':
      case 'Q':
        e.preventDefault()
        setShowQuickActions(!showQuickActions)
        break
      case 'Escape':
        e.preventDefault()
        if (document.fullscreenElement) {
          document.exitFullscreen()
        } else if (showSettings) {
          setShowSettings(false)
        } else if (showBookmarks) {
          setShowBookmarks(false)
        } else if (showTranslationHistory) {
          setShowTranslationHistory(false)
        } else if (showTranslationSettings) {
          setShowTranslationSettings(false)
        } else if (showChapterSummary) {
          setShowChapterSummary(false)
        } else if (showChapterList) {
          setShowChapterList(false)
        }
        break
      case '?':
        e.preventDefault()
        setShowKeyboardHelp(true)
        break
    }
  }

  // 滚动位置跟踪 - 增强版
  const handleScroll = () => {
    if (contentRef.current) {
      const scrollTop = contentRef.current.scrollTop
      const scrollHeight = contentRef.current.scrollHeight
      const clientHeight = contentRef.current.clientHeight

      // 计算阅读位置百分比
      const scrollPercentage = scrollTop / (scrollHeight - clientHeight)
      const position = Math.round(scrollPercentage * 100)

      // 更新阅读进度
      updateProgress(position)
      
      // 自动隐藏工具栏逼辑
      if (autoHideToolbar) {
        setShowToolbar(true)
        if (toolbarTimeout) {
          clearTimeout(toolbarTimeout)
        }
        const timeout = setTimeout(() => {
          setShowToolbar(false)
        }, 3000)
        setToolbarTimeout(timeout)
      }
    }
  }
  
  // 自动滚动功能
  useEffect(() => {
    if (autoScroll && contentRef.current) {
      const interval = setInterval(() => {
        if (contentRef.current) {
          const currentScroll = contentRef.current.scrollTop
          const maxScroll = contentRef.current.scrollHeight - contentRef.current.clientHeight
          
          if (currentScroll < maxScroll) {
            contentRef.current.scrollTop = currentScroll + (scrollSpeed / 10)
          } else {
            // 自动跳转到下一章
            if (currentChapterIndex < allChapters.length - 1) {
              goToNextChapter()
            } else {
              setAutoScroll(false)
              toast.success('已阅读完成！')
            }
          }
        }
      }, 100)
      
      return () => clearInterval(interval)
    }
  }, [autoScroll, scrollSpeed, currentChapterIndex, allChapters.length])

  useEffect(() => {
    if (user && bookId && chapterId) {
      fetchBookAndChapter()
    }
  }, [user, bookId, chapterId])

  // 开始阅读计时
  useEffect(() => {
    if (chapter) {
      startReading()
      return () => {
        stopReading()
      }
    }
  }, [chapter, startReading, stopReading])

  // 添加滚动监听
  useEffect(() => {
    const contentElement = contentRef.current
    if (contentElement) {
      contentElement.addEventListener('scroll', handleScroll)
      return () => {
        contentElement.removeEventListener('scroll', handleScroll)
      }
    }
  }, [chapter])

  // 添加键盘和鼠标事件监听
  useEffect(() => {
    document.addEventListener('keydown', handleKeyPress)
    document.addEventListener('mousemove', handleMouseMove)
    
    // 监听全屏状态变化
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }
    document.addEventListener('fullscreenchange', handleFullscreenChange)
    
    return () => {
      document.removeEventListener('keydown', handleKeyPress)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
      if (toolbarTimeout) {
        clearTimeout(toolbarTimeout)
      }
    }
  }, [autoHideToolbar, toolbarTimeout])

  // 监听主题变化事件，强制重新渲染
  useEffect(() => {
    const handleThemeChange = (event: CustomEvent) => {
      console.log('主题已更改:', event.detail)
      // 强制组件重新渲染以应用新样式
      setForceUpdate(prev => prev + 1)
    }

    window.addEventListener('readerThemeChanged', handleThemeChange as EventListener)
    return () => {
      window.removeEventListener('readerThemeChanged', handleThemeChange as EventListener)
    }
  }, [])

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <nav className="bg-white shadow-sm border-b">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center h-16">
              <Link
                href={`/dashboard/books/${bookId}`}
                className="flex items-center text-gray-500 hover:text-gray-700"
              >
                <ArrowLeft className="h-5 w-5 mr-1" />
                返回书籍详情
              </Link>
            </div>
          </div>
        </nav>
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
        </div>
      </div>
    )
  }

  if (!chapter) {
    return (
      <div className="min-h-screen bg-gray-50">
        <nav className="bg-white shadow-sm border-b">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center h-16">
              <Link
                href={`/dashboard/books/${bookId}`}
                className="flex items-center text-gray-500 hover:text-gray-700"
              >
                <ArrowLeft className="h-5 w-5 mr-1" />
                返回书籍详情
              </Link>
            </div>
          </div>
        </nav>
        <div className="text-center py-12">
          <BookOpen className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">章节不存在</h3>
          <p className="text-gray-500">该章节可能已被删除或您没有访问权限</p>
        </div>
      </div>
    )
  }

  return (
    <div 
      className={`min-h-screen transition-all duration-300 reader-container ${
        isFullscreen ? 'bg-black' : ''
      }`}
      style={getThemeStyles()}
      onMouseMove={handleMouseMove}
    >
      {/* 顶部工具栏 - 增强版 */}
      <div 
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ease-in-out ${
          showToolbar || !autoHideToolbar ? 'translate-y-0' : '-translate-y-full'
        }`}
      >
        <div className="bg-white/95 backdrop-blur-xl border-b border-gray-200/60 shadow-lg">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              {/* 左侧导航 */}
              <div className="flex items-center space-x-4">
                <Link
                  href={`/dashboard/books/${bookId}`}
                  className="group flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100/70 rounded-xl transition-all duration-300 hover:scale-105"
                >
                  <ArrowLeft className="h-4 w-4 mr-2 group-hover:-translate-x-0.5 transition-transform" />
                  <span className="hidden sm:block font-medium">返回</span>
                </Link>
                
                <div className="h-6 w-px bg-gray-300 hidden sm:block" />
                
                <button
                  onClick={() => setShowChapterList(!showChapterList)}
                  className={`group flex items-center px-4 py-2 rounded-xl transition-all duration-300 hover:scale-105 ${
                    showChapterList
                      ? 'text-blue-600 bg-blue-50 hover:bg-blue-100'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100/70'
                  }`}
                >
                  <Sidebar className="h-4 w-4 mr-2 group-hover:rotate-12 transition-transform" />
                  <span className="hidden sm:block font-medium">目录</span>
                </button>
                
                {/* 阅读模式切换 */}
                <div className="hidden lg:flex items-center space-x-1 bg-gray-100 rounded-xl p-1">
                  {(['normal', 'focus', 'immersive'] as const).map((mode) => (
                    <button
                      key={mode}
                      onClick={() => setReadingMode(mode)}
                      className={`px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 ${
                        readingMode === mode
                          ? 'bg-white text-blue-600 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      }`}
                    >
                      {mode === 'normal' ? '标准' : mode === 'focus' ? '专注' : '沉浸'}
                    </button>
                  ))}
                </div>
              </div>

              {/* 中间标题区域 - 精美设计 */}
              <div className="flex-1 text-center px-6">
                <div className="max-w-lg mx-auto">
                  <h1 className="text-base font-semibold text-gray-900 truncate">
                    {chapter.title}
                  </h1>
                  <div className="flex items-center justify-center space-x-3 text-xs text-gray-500 mt-1">
                    <span className="bg-gray-100 px-2 py-0.5 rounded-full">
                      {chapter.book?.title}
                    </span>
                    <span>·</span>
                    <span className="bg-blue-100 px-2 py-0.5 rounded-full text-blue-700">
                      第 {currentChapterIndex + 1} / {allChapters.length} 章
                    </span>
                    {progress && progress.percentage > 0 && (
                      <>
                        <span>·</span>
                        <span className="bg-green-100 px-2 py-0.5 rounded-full text-green-700">
                          {Math.round(progress.percentage)}%
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* 右侧功能按钮 */}
              <div className="flex items-center space-x-3">
                {/* 阅读统计 */}
                <div className="hidden xl:flex items-center space-x-3 text-xs text-gray-500 bg-gradient-to-r from-gray-50 to-blue-50 px-4 py-2 rounded-xl border border-gray-200/50">
                  <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3 text-green-500" />
                    <span className="font-medium">{getFormattedReadingTime()}</span>
                  </div>
                  {saving && (
                    <div className="flex items-center space-x-1">
                      <Save className="h-3 w-3 animate-pulse text-blue-500" />
                      <span className="text-blue-600 font-medium">保存中</span>
                    </div>
                  )}
                  <button
                    onClick={() => setShowReadingStats(!showReadingStats)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <MousePointer2 className="h-3 w-3" />
                  </button>
                </div>

                {/* 功能按钮组 - 精美设计 */}
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => setShowTranslationHistory(true)}
                    className="p-2.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-xl transition-all duration-300 group"
                    title="翻译历史"
                  >
                    <History className="h-4 w-4 group-hover:rotate-12 transition-transform" />
                  </button>

                  <button
                    onClick={toggleImmersiveTranslation}
                    className={`p-2.5 rounded-xl transition-all duration-300 group ${
                      showImmersiveTranslation
                        ? 'text-blue-600 bg-blue-100 hover:bg-blue-200'
                        : 'text-gray-500 hover:text-blue-600 hover:bg-blue-50'
                    }`}
                    title="沉浸式翻译"
                  >
                    <Languages className="h-4 w-4 group-hover:scale-110 transition-transform" />
                  </button>

                  <button
                    onClick={() => setShowChapterSummary(true)}
                    className="p-2.5 text-gray-500 hover:text-purple-600 hover:bg-purple-50 rounded-xl transition-all duration-300 group"
                    title="AI章节总结"
                  >
                    <Coffee className="h-4 w-4 group-hover:rotate-12 transition-transform" />
                  </button>

                  <button
                    onClick={() => setShowBookmarks(true)}
                    className="p-2.5 text-gray-500 hover:text-yellow-600 hover:bg-yellow-50 rounded-xl transition-all duration-300 group"
                    title="书签管理"
                  >
                    <Bookmark className="h-4 w-4 group-hover:scale-110 transition-transform" />
                  </button>

                  <button
                    onClick={() => setShowSettings(true)}
                    className="p-2.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-xl transition-all duration-300 group"
                    title="阅读设置"
                  >
                    <Settings className="h-4 w-4 group-hover:rotate-90 transition-transform" />
                  </button>

                  <div className="h-6 w-px bg-gray-300 mx-2" />

                  <button
                    onClick={toggleFullscreen}
                    className="p-2.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-xl transition-all duration-300 group"
                    title={isFullscreen ? '退出全屏' : '全屏阅读'}
                  >
                    {isFullscreen ? 
                      <Minimize2 className="h-4 w-4 group-hover:scale-90 transition-transform" /> : 
                      <Maximize2 className="h-4 w-4 group-hover:scale-110 transition-transform" />
                    }
                  </button>

                  <button
                    onClick={() => setAutoHideToolbar(!autoHideToolbar)}
                    className={`p-2.5 rounded-xl transition-all duration-300 group ${
                      autoHideToolbar
                        ? 'text-blue-600 bg-blue-100 hover:bg-blue-200'
                        : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                    }`}
                    title={autoHideToolbar ? '关闭自动隐藏' : '自动隐藏工具栏'}
                  >
                    {autoHideToolbar ? 
                      <EyeOff className="h-4 w-4 group-hover:scale-90 transition-transform" /> : 
                      <Eye className="h-4 w-4 group-hover:scale-110 transition-transform" />
                    }
                  </button>
                  
                  <button
                    onClick={() => setShowKeyboardHelp(true)}
                    className="p-2.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-xl transition-all duration-300 group"
                    title="快捷键帮助 (?)"
                  >
                    <svg className="h-4 w-4 group-hover:rotate-12 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 可拖动的章节列表面板 - 暂时隐藏 */}
      {false && showChapterList && (
        <div
          ref={sidebarRef}
          className={`fixed bg-white/95 backdrop-blur-md border border-gray-200/50 rounded-xl z-50 draggable-panel panel-enter ${
            isDragging ? 'dragging' : ''
          }`}
          style={{
            left: `${panelPosition.x}px`,
            top: `${panelPosition.y}px`,
            width: `${sidebarWidth}px`,
            maxHeight: '70vh',
            cursor: isDragging ? 'grabbing' : 'default'
          }}
        >
        <div className="h-full overflow-hidden flex flex-col">
          {/* 可拖动的头部 */}
          <div
            className="p-4 border-b border-gray-200/50 cursor-grab active:cursor-grabbing bg-gradient-to-r from-blue-50 to-purple-50 hover:from-blue-100 hover:to-purple-100 transition-all duration-200 drag-handle"
            onMouseDown={handlePanelMouseDown}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {/* 拖动手柄图标 */}
                <div className="flex flex-col space-y-1">
                  <div className="w-4 h-0.5 bg-gray-400 rounded"></div>
                  <div className="w-4 h-0.5 bg-gray-400 rounded"></div>
                  <div className="w-4 h-0.5 bg-gray-400 rounded"></div>
                </div>
                <List className="h-5 w-5 text-blue-600" />
                <h3 className="font-semibold text-gray-900">章节目录</h3>
                <span className="px-2 py-1 text-xs bg-white/80 text-gray-600 rounded-full font-mono border">
                  {sidebarWidth}px
                </span>
              </div>
              <button
                onClick={() => setShowChapterList(false)}
                className="p-2 text-gray-500 hover:text-gray-700 rounded-lg transition-colors hover:bg-white/80"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            <div className="relative mt-3">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索章节..."
                className="w-full pl-10 pr-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white/80"
              />
            </div>
          </div>

          {/* 章节列表内容 - 可滚动 */}
          <div className="flex-1 overflow-y-auto p-2">
            {allChapters.map((ch, index) => (
              <button
                key={ch.id}
                onClick={() => {
                  navigateToChapter(index)
                  setShowChapterList(false)
                }}
                className={`w-full text-left p-3 rounded-lg transition-all duration-200 mb-1 ${
                  index === currentChapterIndex
                    ? 'bg-blue-50 text-blue-700 border border-blue-200'
                    : 'hover:bg-gray-50 text-gray-700'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="text-sm font-medium line-clamp-2">
                      {ch.title}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      第{index + 1}章 · {ch.word_count?.toLocaleString() || 0} 字
                    </div>
                  </div>
                  {index === currentChapterIndex && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full ml-2" />
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* 右下角调整大小手柄 */}
        <div
          ref={resizerRef}
          className={`absolute bottom-0 right-0 w-4 h-4 cursor-nw-resize bg-gradient-to-tl from-blue-400 to-purple-400 rounded-tl-lg opacity-60 hover:opacity-100 transition-all duration-200 resize-handle ${
            isResizing ? 'opacity-100 scale-110' : ''
          }`}
          onMouseDown={handleMouseDown}
          onDoubleClick={handleDoubleClick}
          title="拖拽调整大小（双击重置）"
        >
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-2 h-2 border-r border-b border-white/80 transform rotate-45"></div>
          </div>

          {/* 宽度显示提示 */}
          {isResizing && (
            <div className="absolute -top-8 -left-12 bg-gray-900 text-white px-2 py-1 rounded text-xs whitespace-nowrap">
              {sidebarWidth}px
            </div>
          )}
        </div>
        </div>
      )}

      {/* 主要阅读区域 */}
      <main
        className={`content-transition pt-14`}
        style={{
          backgroundColor: 'var(--reader-bg)',
          minHeight: '100vh',
        }}
      >
        {/* 阅读进度条 - 简洁设计 */}
        <div className="h-1 bg-gray-200">
          <div 
            className="h-1 bg-blue-600 transition-all duration-300"
            style={{
              width: `${progress?.percentage || 0}%`,
            }}
          />
        </div>
        
        {/* 阅读内容容器 - 增大阅读区域 */}
        <div 
          className={`${readingMode === 'focus' ? 'max-w-4xl' : readingMode === 'immersive' ? 'max-w-7xl' : 'max-w-6xl'} mx-auto transition-all duration-500 px-4`}
        >
          {/* 章节内容 */}
          <div
            ref={contentRef}
            className={`reader-content-wrapper overflow-y-auto relative ${
              readingMode === 'focus' ? 'focus-mode' : ''
            } ${lineHighlight ? 'line-highlight' : ''}`}
            style={{
              height: isFullscreen ? '100vh' : 'calc(100vh - 100px)',
              maxWidth: currentSettings?.page_width ? `${currentSettings.page_width}px` : '1200px',
              margin: '0 auto',
              padding: isFullscreen ? '2rem 1.5rem' : '2rem 1.5rem 1rem',
              scrollBehavior: 'smooth',
            }}
          >
            {/* 章节标题 - 简洁设计 */}
            <header className="text-center mb-12">
              <h1
                className="text-2xl md:text-3xl font-medium mb-8 text-gray-900"
                style={{
                  fontSize: currentSettings?.font_size ? `${Math.min(currentSettings.font_size + 8, 32)}px` : '24px',
                  fontFamily: 'var(--reader-font-family)',
                  lineHeight: 1.3,
                  color: 'var(--reader-text)',
                }}
              >
                {chapter.title}
              </h1>
            </header>

            {/* 章节正文 - 纯净设计 */}
            <article 
              className="reader-article max-w-none"
              style={{
                color: 'var(--reader-text)',
                fontSize: currentSettings?.font_size ? `${currentSettings.font_size}px` : '18px',
                lineHeight: currentSettings?.line_height || 1.8,
                fontFamily: currentSettings?.font_family || 'var(--reader-font-family)',
                textAlign: (currentSettings?.text_align as any) || 'left',
              }}
            >
              <div 
                className="chapter-content whitespace-pre-wrap"
                style={{
                  textIndent: '2em',
                  textRendering: 'optimizeLegibility',
                  WebkitFontSmoothing: 'antialiased',
                  MozOsxFontSmoothing: 'grayscale',
                }}
              >
                {chapter.content}
              </div>
            </article>

            {/* 章节尾部 - 简洁设计 */}
            <footer className="mt-16 pt-8 text-center">
              {/* 章节导航按钮 - 简化设计 */}
              <div className="flex justify-center items-center space-x-4">
                <button
                  onClick={goToPreviousChapter}
                  disabled={currentChapterIndex <= 0}
                  className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-40 disabled:cursor-not-allowed"
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  上一章
                </button>
                
                <Link
                  href={`/dashboard/books/${bookId}`}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Home className="h-4 w-4 mr-1" />
                  返回目录
                </Link>
                
                <button
                  onClick={goToNextChapter}
                  disabled={currentChapterIndex >= allChapters.length - 1}
                  className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-40 disabled:cursor-not-allowed"
                >
                  下一章
                  <ChevronRight className="h-4 w-4 ml-1" />
                </button>
              </div>
            </footer>
          </div>
        </div>
      </main>

      {/* 右侧浮动快捷操作工具 - 新设计 */}
      {/* 显示/隐藏切换按钮 - 始终显示 */}
      <div className="fixed right-6 top-1/2 transform -translate-y-1/2 z-50">
        <button
          onClick={() => setShowQuickActions(!showQuickActions)}
          className={`floating-button w-10 h-10 backdrop-blur-xl rounded-full shadow-xl border border-gray-200/50 flex items-center justify-center transition-all duration-300 hover:scale-110 ${
            showQuickActions
              ? 'bg-blue-500 text-white border-blue-300'
              : 'bg-white/95 text-gray-600 hover:text-blue-600 hover:bg-blue-50'
          }`}
          title={showQuickActions ? '隐藏快捷按钮' : '显示快捷按钮'}
        >
          {showQuickActions ? (
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ) : (
            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
            </svg>
          )}
        </button>
      </div>
      
      {/* 快捷操作面板 */}
      {showQuickActions && (
        <div className="floating-actions fixed right-20 top-1/2 transform -translate-y-1/2 z-40 space-y-4">
          {/* 书签快捷按钮 */}
          <div className="group relative">
            <div className="floating-button bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden">
              <BookmarkButton
                bookId={bookId}
                chapterId={chapterId}
                position={0}
                scrollPosition={contentRef.current?.scrollTop || 0}
                className="!rounded-2xl !shadow-none !border-0 hover:bg-blue-50 transition-all duration-300 group-hover:scale-105"
              />
            </div>
            <div className="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-1.5 rounded-lg text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none">
              添加书签
              <div className="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
            </div>
          </div>
          
          {/* 阅读模式切换 */}
          <div className="group relative">
            <button
              onClick={() => {
                const modes: ('normal' | 'focus' | 'immersive')[] = ['normal', 'focus', 'immersive']
                const currentIndex = modes.indexOf(readingMode)
                const nextMode = modes[(currentIndex + 1) % modes.length]
                setReadingMode(nextMode)
              }}
              className="floating-button w-14 h-14 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 flex items-center justify-center text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-all duration-300 group-hover:scale-105"
              title="阅读模式"
            >
              {readingMode === 'normal' ? <Type className="h-6 w-6" /> : 
               readingMode === 'focus' ? <Eye className="h-6 w-6" /> : <Maximize2 className="h-6 w-6" />}
            </button>
            <div className="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-1.5 rounded-lg text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none">
              {readingMode === 'normal' ? '标准模式' : readingMode === 'focus' ? '专注模式' : '沉浸模式'}
              <div className="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
            </div>
          </div>
          
          {/* 翻译功能快捷按钮 */}
          <div className="group relative">
            <button
              onClick={toggleImmersiveTranslation}
              className={`floating-button w-14 h-14 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 flex items-center justify-center transition-all duration-300 group-hover:scale-105 ${
                showImmersiveTranslation
                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white border-blue-300'
                  : 'bg-white/95 text-gray-600 hover:text-blue-600 hover:bg-blue-50'
              }`}
              title="沉浸式翻译"
            >
              <Languages className="h-6 w-6 group-hover:rotate-12 transition-transform" />
            </button>
            <div className="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-1.5 rounded-lg text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none">
              沉浸式翻译
              <div className="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
            </div>
          </div>
          
          {/* 自动滚动控制 */}
          <div className="group relative">
            <button
              onClick={() => setAutoScroll(!autoScroll)}
              className={`floating-button w-14 h-14 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 flex items-center justify-center transition-all duration-300 group-hover:scale-105 ${
                autoScroll
                  ? 'bg-gradient-to-r from-green-500 to-blue-500 text-white border-green-300'
                  : 'bg-white/95 text-gray-600 hover:text-green-600 hover:bg-green-50'
              }`}
              title="自动滚动"
            >
              {autoScroll ? 
                <Pause className="h-6 w-6 group-hover:scale-110 transition-transform" /> :
                <Play className="h-6 w-6 group-hover:scale-110 transition-transform" />
              }
            </button>
            <div className="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-1.5 rounded-lg text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none">
              {autoScroll ? '停止自动滚动' : '开始自动滚动'}
              <div className="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
            </div>
          </div>
          
          {/* 阅读设置 */}
          <div className="group relative">
            <button
              onClick={() => setShowSettings(true)}
              className="floating-button w-14 h-14 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-300 group-hover:scale-105"
              title="阅读设置"
            >
              <Settings className="h-6 w-6 group-hover:rotate-180 transition-transform duration-500" />
            </button>
            <div className="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-1.5 rounded-lg text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none">
              阅读设置
              <div className="absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900"></div>
            </div>
          </div>
        </div>
      )}

      {/* 左侧章节导航 - 增强设计 */}
       <div className="floating-navigation fixed left-6 top-1/2 transform -translate-y-1/2 z-40 display">
        <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 p-2 space-y-2">
          <button
            onClick={goToPreviousChapter}
            disabled={currentChapterIndex <= 0}
            className="group w-12 h-12 rounded-xl flex items-center justify-center text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300 disabled:opacity-30 disabled:cursor-not-allowed disabled:hover:bg-transparent disabled:hover:text-gray-600"
            title="上一章"
          >
            <ChevronLeft className="h-6 w-6 group-hover:-translate-x-0.5 transition-transform" />
          </button>
          
          {/* 章节进度指示 */}
          <div className="flex items-center justify-center py-2">
            <div className="text-xs text-gray-500 font-medium">
              {currentChapterIndex + 1}
            </div>
            <div className="w-8 h-0.5 mx-2 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className="h-full bg-gradient-to-r from-blue-400 to-purple-500 transition-all duration-500"
                style={{ width: `${((currentChapterIndex + 1) / allChapters.length) * 100}%` }}
              />
            </div>
            <div className="text-xs text-gray-500 font-medium">
              {allChapters.length}
            </div>
          </div>
          
          <button
            onClick={goToNextChapter}
            disabled={currentChapterIndex >= allChapters.length - 1}
            className="group w-12 h-12 rounded-xl flex items-center justify-center text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300 disabled:opacity-30 disabled:cursor-not-allowed disabled:hover:bg-transparent disabled:hover:text-gray-600"
            title="下一章"
          >
            <ChevronRight className="h-6 w-6 group-hover:translate-x-0.5 transition-transform" />
          </button>
        </div>
          
        {/* 章节信息提示 */}
        {allChapters[currentChapterIndex] && (
          <div className="mt-3 bg-gray-900/90 backdrop-blur-sm text-white px-3 py-2 rounded-xl text-xs max-w-48 shadow-lg">
            <div className="font-medium truncate">
              {allChapters[currentChapterIndex].title}
            </div>
            <div className="text-gray-300 mt-1">
              {chapter.word_count?.toLocaleString() || 0} 字 · 约 {Math.ceil((chapter.word_count || 0) / 300)} 分钟
            </div>
          </div>
        )}
      </div>

      {/* 阅读器设置面板 */}
      <ReaderSettingsPanel
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />

      {/* 翻译弹窗 */}
      <TranslationPopup
        visible={showTranslation}
        selectedText={selectedText}
        position={selectionPosition}
        onClose={hideTranslation}
        bookId={bookId}
        chapterId={chapterId}
        textPosition={getTextPosition()}
      />

      {/* 翻译历史面板 */}
      <TranslationHistory
        visible={showTranslationHistory}
        onClose={() => setShowTranslationHistory(false)}
      />

      {/* 翻译设置面板 */}
      <TranslationSettings
        visible={showTranslationSettings}
        onClose={() => setShowTranslationSettings(false)}
      />

      {/* 书签列表面板 */}
      {showBookmarks && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">书签列表</h2>
              <button
                onClick={() => setShowBookmarks(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="p-6 overflow-y-auto max-h-[60vh]">
              <BookmarkList
                bookId={bookId}
                onBookmarkClick={handleBookmarkJump}
              />
            </div>
          </div>
        </div>
      )}

      {/* 键盘快捷键帮助面板 */}
      {showKeyboardHelp && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 backdrop-blur-sm">
          <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-gray-200/50 max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200/50">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-gray-900 flex items-center">
                  <MousePointer2 className="h-5 w-5 mr-3 text-blue-500" />
                  快捷键指南
                </h2>
                <button
                  onClick={() => setShowKeyboardHelp(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-xl transition-all duration-200"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-96">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                    <ChevronRight className="h-4 w-4 mr-2 text-green-500" />
                    导航操作
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">上一章</span>
                      <div className="flex space-x-1">
                        <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">←</kbd>
                        <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">H</kbd>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">下一章</span>
                      <div className="flex space-x-1">
                        <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">→</kbd>
                        <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">L</kbd>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">章节目录</span>
                      <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">C</kbd>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                    <Settings className="h-4 w-4 mr-2 text-blue-500" />
                    设置操作
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">阅读设置</span>
                      <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">S</kbd>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">全屏模式</span>
                      <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">F</kbd>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">阅读模式</span>
                      <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">M</kbd>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                    <Bookmark className="h-4 w-4 mr-2 text-yellow-500" />
                    书签操作
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">书签列表</span>
                      <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">B</kbd>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">阅读统计</span>
                      <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">R</kbd>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">快捷按钮</span>
                      <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Q</kbd>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                    <Languages className="h-4 w-4 mr-2 text-purple-500" />
                    翻译功能
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">沉浸式翻译</span>
                      <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">T</kbd>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">增大字体</span>
                      <div className="flex space-x-1">
                        <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl</kbd>
                        <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">+</kbd>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">减小字体</span>
                      <div className="flex space-x-1">
                        <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl</kbd>
                        <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">-</kbd>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">重置字体</span>
                      <div className="flex space-x-1">
                        <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Ctrl</kbd>
                        <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">0</kbd>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-blue-900">提示</span>
                </div>
                <p className="text-xs text-blue-800">
                  按 <kbd className="px-1 py-0.5 bg-blue-200 rounded text-xs">?</kbd> 可以随时查看此帮助面板。
                  在阅读过程中，您可以使用这些快捷键来提高阅读效率。
                </p>
              </div>
            </div>
            
            <div className="p-6 border-t border-gray-200/50 bg-gray-50/50">
              <button
                onClick={() => setShowKeyboardHelp(false)}
                className="w-full px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl transition-all duration-200 font-medium"
              >
                了解了，开始阅读
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 阅读统计面板 */}
      {showReadingStats && (
        <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
          <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 p-4 min-w-80 fade-in-scale">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900 flex items-center">
                <MousePointer2 className="h-4 w-4 mr-2 text-blue-500" />
                阅读统计
              </h3>
              <button
                onClick={() => setShowReadingStats(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="text-center p-3 bg-blue-50 rounded-xl">
                <div className="text-xl font-bold text-blue-600">{getFormattedReadingTime()}</div>
                <div className="text-blue-700">阅读时间</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-xl">
                <div className="text-xl font-bold text-green-600">{Math.round(progress?.percentage || 0)}%</div>
                <div className="text-green-700">章节进度</div>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded-xl">
                <div className="text-xl font-bold text-purple-600">{chapter?.word_count?.toLocaleString() || 0}</div>
                <div className="text-purple-700">章节字数</div>
              </div>
              <div className="text-center p-3 bg-orange-50 rounded-xl">
                <div className="text-xl font-bold text-orange-600">{Math.ceil((chapter?.word_count || 0) / ((totalReadingTime / 60000) || 1))}</div>
                <div className="text-orange-700">阅读速度(字/分)</div>
              </div>
            </div>
            <div className="mt-4 flex justify-center space-x-2">
              <button
                onClick={() => setWordHighlight(!wordHighlight)}
                className={`px-3 py-1.5 text-xs rounded-lg transition-all duration-200 ${
                  wordHighlight ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                字词高亮
              </button>
              <button
                onClick={() => setLineHighlight(!lineHighlight)}
                className={`px-3 py-1.5 text-xs rounded-lg transition-all duration-200 ${
                  lineHighlight ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                行高亮
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 快速字体调节工具 - 功能修复 */}
      <div className="fixed bottom-6 right-6 z-40">
        <div className="flex items-center space-x-2 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 p-3 hover:shadow-2xl transition-all duration-300">
          <button
            onClick={() => {
              const currentSize = currentSettings?.font_size || 18
              const newSize = Math.max(12, currentSize - 2)
              if (newSize !== currentSize) {
                updateTempSetting('font_size', newSize)
                // 静默保存设置，不显示提示
                setTimeout(() => {
                  saveTempSettings({ silent: true })
                }, 100)
              }
            }}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-xl transition-all duration-200 hover:scale-105"
            title="减小字体 (Ctrl+-)"
          >
            <ZoomOut className="h-4 w-4" />
          </button>
          
          <div className="text-xs text-gray-700 font-semibold min-w-16 text-center bg-gradient-to-r from-blue-50 to-indigo-50 px-3 py-2 rounded-lg border border-blue-200">
            <span className="text-blue-600">{currentSettings?.font_size || 18}</span>
            <span className="text-gray-500">px</span>
          </div>
          
          <button
            onClick={() => {
              const currentSize = currentSettings?.font_size || 18
              const newSize = Math.min(36, currentSize + 2)
              if (newSize !== currentSize) {
                updateTempSetting('font_size', newSize)
                // 静默保存设置，不显示提示
                setTimeout(() => {
                  saveTempSettings({ silent: true })
                }, 100)
              }
            }}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-xl transition-all duration-200 hover:scale-105"
            title="增大字体 (Ctrl++)"
          >
            <ZoomIn className="h-4 w-4" />
          </button>
          
          <div className="h-6 w-px bg-gray-300" />
          
          <button
            onClick={() => {
              const defaultSize = 18
              const currentSize = currentSettings?.font_size || 18
              if (currentSize !== defaultSize) {
                updateTempSetting('font_size', defaultSize)
                // 静默保存设置，不显示提示
                setTimeout(() => {
                  saveTempSettings({ silent: true })
                }, 100)
              }
            }}
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-xl transition-all duration-200 hover:scale-105"
            title="重置字体大小 (Ctrl+0)"
          >
            <RotateCcw className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* 沉浸式翻译组件 */}
      {showImmersiveTranslation && chapter && (
        <ImmersiveTranslation
          enabled={showImmersiveTranslation}
          content={chapter.content}
          bookId={bookId}
          chapterId={chapterId}
          splitRatio={translationSettings?.immersive_split_ratio || 0.5}
          autoTranslate={translationSettings?.immersive_auto_translate || false}
          targetLanguage={translationSettings?.default_target_language || 'zh-CN'}
          sourceLanguage={translationSettings?.default_source_language || 'auto'}
          onToggle={toggleImmersiveTranslation}
        />
      )}

      {/* 章节总结组件 */}
      <ChapterSummary
        visible={showChapterSummary}
        onClose={() => setShowChapterSummary(false)}
        bookId={bookId}
        chapterId={chapterId}
        chapterTitle={chapter.title}
      />
    </div>
  )
}
