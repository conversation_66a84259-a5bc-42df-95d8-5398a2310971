import axios from 'axios'
import Cookies from 'js-cookie'
import toast from 'react-hot-toast'

// 创建 axios 实例
const api = axios.create({
  baseURL: process.env.NODE_ENV === 'production' 
    ? 'https://your-api-domain.com/api' 
    : 'http://127.0.0.1:8000/api',
  timeout: 30000, // 增加到30秒
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 添加认证 token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理错误和 token 刷新
api.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    const originalRequest = error.config

    // 如果是 401 错误且不是刷新 token 的请求
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        const refreshToken = Cookies.get('refresh_token')
        if (refreshToken) {
          const response = await axios.post(
            `${api.defaults.baseURL}/auth/token/refresh/`,
            { refresh: refreshToken }
          )
          
          const { access } = response.data
          Cookies.set('access_token', access, { expires: 1 }) // 1天过期
          
          // 重新发送原请求
          originalRequest.headers.Authorization = `Bearer ${access}`
          return api(originalRequest)
        }
      } catch (refreshError) {
        // 刷新失败，清除所有 token 并跳转到登录页
        Cookies.remove('access_token')
        Cookies.remove('refresh_token')
        window.location.href = '/auth/login'
        return Promise.reject(refreshError)
      }
    }

    // 显示错误消息 - 不在这里显示，让组件自己处理
    // 这样可以提供更详细的错误信息

    return Promise.reject(error)
  }
)

// 用户认证相关 API
export const authAPI = {
  // 用户注册
  register: async (data: {
    username: string
    email: string
    password: string
    password_confirm: string
    display_name?: string
  }) => {
    const response = await api.post('/auth/register/', data)
    return response.data
  },

  // 用户登录
  login: async (data: { email: string; password: string }) => {
    const response = await api.post('/auth/login/', data)
    return response.data
  },

  // 获取用户资料
  getProfile: async () => {
    const response = await api.get('/auth/profile/')
    return response.data
  },

  // 更新用户资料
  updateProfile: async (data: { display_name?: string; avatar?: string }) => {
    const response = await api.put('/auth/profile/', data)
    return response.data
  },

  // 修改密码
  changePassword: async (data: {
    old_password: string
    new_password: string
    new_password_confirm: string
  }) => {
    const response = await api.post('/auth/password/change/', data)
    return response.data
  },

  // 用户登出
  logout: async () => {
    const response = await api.post('/auth/logout/')
    return response.data
  },

  // 验证 token
  verifyToken: async () => {
    const response = await api.get('/auth/token/verify/')
    return response.data
  },
}

// 工具函数
export const setAuthTokens = (tokens: { access: string; refresh: string }) => {
  Cookies.set('access_token', tokens.access, { expires: 1 }) // 1天过期
  Cookies.set('refresh_token', tokens.refresh, { expires: 7 }) // 7天过期
}

export const clearAuthTokens = () => {
  Cookies.remove('access_token')
  Cookies.remove('refresh_token')
}

export const getAccessToken = () => {
  return Cookies.get('access_token')
}

export const getRefreshToken = () => {
  return Cookies.get('refresh_token')
}

export default api
