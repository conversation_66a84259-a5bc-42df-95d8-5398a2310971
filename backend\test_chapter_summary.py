#!/usr/bin/env python
"""
测试章节总结API的脚本
"""

import os
import sys
import django
from django.conf import settings

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ebook_platform.settings')
django.setup()

from apps.books.models import Book, Chapter
from django.contrib.auth import get_user_model
from apps.ai.services import AIService

User = get_user_model()

def test_chapter_summary_api():
    """测试章节总结API"""
    print("🔍 测试章节总结API...")
    
    try:
        # 查找用户和书籍
        users = User.objects.all()
        if not users:
            print("❌ 没有找到用户")
            return
        
        user = users.first()
        print(f"✅ 找到用户: {user.username}")
        
        # 查找用户的书籍
        books = Book.objects.filter(user=user, status='ready')
        if not books:
            print("❌ 没有找到该用户的书籍")
            return
        
        book = books.first()
        print(f"✅ 找到书籍: {book.title}")
        
        # 查找章节
        chapters = Chapter.objects.filter(book=book)
        if not chapters:
            print("❌ 没有找到章节")
            return
        
        chapter = chapters.first()
        print(f"✅ 找到章节: {chapter.title}")
        
        # 测试获取章节内容
        from apps.books.views import _get_chapter_content_from_file
        content = _get_chapter_content_from_file(book, chapter)
        
        if not content:
            print("❌ 无法获取章节内容")
            return
        
        print(f"✅ 章节内容长度: {len(content)} 字符")
        print(f"📝 内容预览: {content[:200]}...")
        
        # 测试AI服务
        print("\n🤖 测试AI服务...")
        try:
            ai_service = AIService()
            print("✅ AI服务初始化成功")
            
            # 生成总结
            print("🔄 正在生成章节总结...")
            result = ai_service.generate_chapter_summary(
                chapter_title=chapter.title,
                chapter_content=content[:2000],  # 限制长度以节省token
                language='zh-CN'
            )
            
            print(f"📊 总结结果: {result}")
            
            if result.get('success'):
                print("✅ 章节总结生成成功！")
                print(f"📖 总结内容: {result.get('summary', '')[:200]}...")
                print(f"📊 字数统计: 原文{result.get('word_count')}字, 总结{result.get('summary_word_count')}字")
            else:
                print(f"❌ 总结生成失败: {result.get('error')}")
                
        except Exception as e:
            print(f"❌ AI服务错误: {str(e)}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def check_ai_config():
    """检查AI配置"""
    print("\n🔧 检查AI配置...")
    
    try:
        from apps.ai.config import AIConfig
        config = AIConfig.get_openai_config()
        
        print(f"📝 API Key: {'已配置' if config.get('api_key') else '未配置'}")
        print(f"🔗 Base URL: {config.get('base_url', '默认')}")
        print(f"🤖 Model: {config.get('model', '默认')}")
        print(f"⏱️ Timeout: {config.get('timeout', '默认')}秒")
        
        if not config.get('api_key'):
            print("⚠️  警告: OpenAI API Key 未配置，请检查环境变量或配置文件")
            
    except Exception as e:
        print(f"❌ 配置检查失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始测试章节总结功能...\n")
    
    # 检查AI配置
    check_ai_config()
    
    # 测试API
    test_chapter_summary_api()
    
    print("\n✨ 测试完成！")
