import React, { useState, useEffect, useRef } from 'react';
import { translationAPI } from '@/lib/translationAPI';
import './translation.css';

interface TranslationPopupProps {
  visible: boolean;
  selectedText: string;
  position: { x: number; y: number };
  onClose: () => void;
  bookId?: string;
  chapterId?: string;
  textPosition?: number;
}

const LANGUAGE_OPTIONS = [
  { value: 'auto', label: '自动检测' },
  { value: 'zh-CN', label: '中文' },
  { value: 'en', label: 'English' },
  { value: 'ja', label: '日本語' },
  { value: 'ko', label: '한국어' },
  { value: 'fr', label: 'Français' },
  { value: 'de', label: 'Deutsch' },
  { value: 'es', label: 'Español' },
];

const TranslationPopup: React.FC<TranslationPopupProps> = ({
  visible,
  selectedText,
  position,
  onClose,
  bookId,
  chapterId,
  textPosition
}) => {
  const [loading, setLoading] = useState(false);
  const [translation, setTranslation] = useState<any>(null);
  const [sourceLang, setSourceLang] = useState('auto');
  const [targetLang, setTargetLang] = useState('zh-CN');
  const [isFavorited, setIsFavorited] = useState(false);
  const popupRef = useRef<HTMLDivElement>(null);

  // 当选中文本变化时，自动翻译
  useEffect(() => {
    if (visible && selectedText && selectedText.trim()) {
      handleTranslate();
    }
  }, [visible, selectedText]);

  // 点击外部关闭弹窗 - 增强逻辑
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (popupRef.current && !popupRef.current.contains(target)) {
        // 检查是否点击的是阅读内容区域，如果是则不关闭弹窗
        const clickedElement = target as HTMLElement;
        const isContentClick = clickedElement.closest('.chapter-content') || 
                              clickedElement.closest('.reader-content') ||
                              clickedElement.closest('.reader-article');
        
        if (!isContentClick) {
          onClose();
        }
      }
    };

    // 键盘ESC关闭
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (visible) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [visible, onClose]);

  const handleTranslate = async () => {
    if (!selectedText || !selectedText.trim()) {
      return;
    }

    setLoading(true);
    try {
      const response = await translationAPI.translate({
        text: selectedText.trim(),
        source_language: sourceLang,
        target_language: targetLang,
        book_id: bookId,
        chapter_id: chapterId,
        position: textPosition
      });

      if (response.success) {
        setTranslation(response);
        setIsFavorited(false);
      } else {
        console.error('Translation failed:', response.error);
        setTranslation(null);
      }
    } catch (error) {
      console.error('Translation error:', error);
      setTranslation(null);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyTranslation = () => {
    if (translation?.translation) {
      navigator.clipboard.writeText(translation.translation);
    }
  };

  const handleToggleFavorite = async () => {
    if (!translation) return;
    setIsFavorited(!isFavorited);
  };

  const handleLanguageChange = (type: 'source' | 'target', value: string) => {
    if (type === 'source') {
      setSourceLang(value);
    } else {
      setTargetLang(value);
    }
    
    if (translation) {
      setTimeout(handleTranslate, 100);
    }
  };

  if (!visible) return null;

  // 智能定位逻辑
  const getSmartPosition = () => {
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile) {
      // 移动端显示在屏幕中央
      return {
        position: 'fixed' as const,
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)',
        zIndex: 1000,
        maxWidth: '95vw',
        minWidth: '280px',
      };
    }
    
    // 桌面端显示在选择位置附近
    return {
      position: 'fixed' as const,
      left: Math.max(20, Math.min(window.innerWidth - 420, position.x)),
      top: Math.max(20, Math.min(window.innerHeight - 250, position.y)),
      zIndex: 1000,
      maxWidth: '400px',
      minWidth: '320px',
    };
  };
  
  const popupStyle = getSmartPosition();

  return (
    <div 
      ref={popupRef}
      className="translation-popup"
      style={popupStyle}
    >
      <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 p-5 transition-all duration-300 hover:shadow-3xl">
        {/* 背景模糊叠加层 */}
        <div className="absolute inset-0 bg-white/80 backdrop-blur-md rounded-2xl -z-10"></div>
        {/* 头部 */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9.578a18.87 18.87 0 01-1.724 4.78c.29.354.596.696.914 1.026a1 1 0 11-1.44 1.389c-.188-.196-.373-.396-.554-.6a19.098 19.098 0 01-3.107 3.567 1 1 0 01-1.334-1.49 17.087 17.087 0 003.13-3.733 18.992 18.992 0 01-1.487-2.494 1 1 0 111.79-.89c.234.47.489.928.764 1.372.417-.934.752-1.913.997-2.927H3a1 1 0 110-2h3V3a1 1 0 011-1zm6 6a1 1 0 01.894.553l2.991 5.982a.869.869 0 01.02.037l.99 1.98a1 1 0 11-1.79.895L15.383 16h-4.764l-.724 1.447a1 1 0 11-1.788-.894l.99-1.98.019-.038 2.99-5.982A1 1 0 0113 8zm-1.382 6h2.764L13 11.236 11.618 14z" clipRule="evenodd" />
            </svg>
            <span className="text-sm font-medium text-gray-700">翻译</span>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 语言选择 */}
        <div className="flex items-center justify-center space-x-2 mb-3">
          <select
            value={sourceLang}
            onChange={(e) => handleLanguageChange('source', e.target.value)}
            className="text-xs border border-gray-300 rounded px-2 py-1"
          >
            {LANGUAGE_OPTIONS.map(lang => (
              <option key={lang.value} value={lang.value}>
                {lang.label}
              </option>
            ))}
          </select>
          
          <button
            onClick={() => {
              const temp = sourceLang;
              setSourceLang(targetLang);
              setTargetLang(temp);
              if (translation) {
                setTimeout(handleTranslate, 100);
              }
            }}
            className="p-1 text-gray-400 hover:text-gray-600"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
            </svg>
          </button>
          
          <select
            value={targetLang}
            onChange={(e) => handleLanguageChange('target', e.target.value)}
            className="text-xs border border-gray-300 rounded px-2 py-1"
          >
            {LANGUAGE_OPTIONS.filter(lang => lang.value !== 'auto').map(lang => (
              <option key={lang.value} value={lang.value}>
                {lang.label}
              </option>
            ))}
          </select>
        </div>

        {/* 原文 */}
        <div className="bg-gradient-to-r from-gray-50 to-blue-50 p-4 rounded-xl mb-4 border-l-4 border-gray-400 relative overflow-hidden">
          <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-gray-400 to-blue-400"></div>
          <div className="text-xs text-gray-600 mb-2 font-medium">原文：</div>
          <div className="text-sm text-gray-800 leading-relaxed font-medium">
            {selectedText}
          </div>
        </div>

        {/* 翻译结果 */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl mb-4 border-l-4 border-blue-500 min-h-[80px] relative overflow-hidden">
          <div className="absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-blue-500 to-indigo-500"></div>
          <div className="text-xs text-gray-500 mb-1">译文：</div>
          
          {loading ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
              <span className="ml-2 text-xs text-gray-500">翻译中...</span>
            </div>
          ) : translation ? (
            <>
              <div className="text-sm text-blue-800 leading-relaxed mb-2">
                {translation.translation}
              </div>
              
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>
                  {translation.detected_language} → {translation.target_language}
                </span>
                <span>
                  置信度: {(translation.confidence * 100).toFixed(0)}%
                  {translation.cached && ' (缓存)'}
                </span>
              </div>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center py-4 text-gray-400">
              <svg className="w-6 h-6 mb-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9.578a18.87 18.87 0 01-1.724 4.78c.29.354.596.696.914 1.026a1 1 0 11-1.44 1.389c-.188-.196-.373-.396-.554-.6a19.098 19.098 0 01-3.107 3.567 1 1 0 01-1.334-1.49 17.087 17.087 0 003.13-3.733 18.992 18.992 0 01-1.487-2.494 1 1 0 111.79-.89c.234.47.489.928.764 1.372.417-.934.752-1.913.997-2.927H3a1 1 0 110-2h3V3a1 1 0 011-1zm6 6a1 1 0 01.894.553l2.991 5.982a.869.869 0 01.02.037l.99 1.98a1 1 0 11-1.79.895L15.383 16h-4.764l-.724 1.447a1 1 0 11-1.788-.894l.99-1.98.019-.038 2.99-5.982A1 1 0 0113 8zm-1.382 6h2.764L13 11.236 11.618 14z" clipRule="evenodd" />
              </svg>
              <span className="text-xs">点击翻译按钮开始翻译</span>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center justify-between">
          <button
            onClick={handleTranslate}
            disabled={loading}
            className="flex items-center space-x-1 px-3 py-1.5 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 disabled:opacity-50"
          >
            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9.578a18.87 18.87 0 01-1.724 4.78c.29.354.596.696.914 1.026a1 1 0 11-1.44 1.389c-.188-.196-.373-.396-.554-.6a19.098 19.098 0 01-3.107 3.567 1 1 0 01-1.334-1.49 17.087 17.087 0 003.13-3.733 18.992 18.992 0 01-1.487-2.494 1 1 0 111.79-.89c.234.47.489.928.764 1.372.417-.934.752-1.913.997-2.927H3a1 1 0 110-2h3V3a1 1 0 011-1zm6 6a1 1 0 01.894.553l2.991 5.982a.869.869 0 01.02.037l.99 1.98a1 1 0 11-1.79.895L15.383 16h-4.764l-.724 1.447a1 1 0 11-1.788-.894l.99-1.98.019-.038 2.99-5.982A1 1 0 0113 8zm-1.382 6h2.764L13 11.236 11.618 14z" clipRule="evenodd" />
            </svg>
            <span>翻译</span>
          </button>
          
          {translation && (
            <div className="flex items-center space-x-2">
              <button
                onClick={handleCopyTranslation}
                className="flex items-center space-x-1 px-2 py-1 text-xs text-gray-600 hover:text-gray-800"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                <span>复制</span>
              </button>
              
              <button
                onClick={handleToggleFavorite}
                className={`flex items-center space-x-1 px-2 py-1 text-xs ${
                  isFavorited ? 'text-yellow-600' : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                <svg className="w-3 h-3" fill={isFavorited ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                </svg>
                <span>{isFavorited ? '已收藏' : '收藏'}</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TranslationPopup;
