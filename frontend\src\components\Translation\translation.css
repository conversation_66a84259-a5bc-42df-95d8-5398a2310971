/* 翻译组件通用样式 */

/* 滑块样式 */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 翻译弹窗动画 */
.translation-popup {
  animation: fadeInUp 0.2s ease-out;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 - 增强版 */
@media (max-width: 640px) {
  .translation-popup {
    max-width: 95vw !important;
    min-width: 300px !important;
    margin: 0 10px;
    transform: translateX(-50%) translateY(-50%);
    left: 50% !important;
    top: 50% !important;
  }
  
  .translation-popup .bg-white {
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .translation-popup {
    max-width: 98vw !important;
    min-width: 280px !important;
    margin: 0 5px;
  }
  
  .translation-popup .p-4 {
    padding: 16px 12px;
  }
  
  .translation-popup .text-sm {
    font-size: 13px;
  }
}

/* 沉浸式翻译样式 */
.immersive-translation-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.immersive-controls {
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.immersive-content {
  flex: 1;
  overflow: hidden;
}

.original-column,
.translation-column {
  height: 100%;
}

.paragraph-item {
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.paragraph-item:hover {
  border-color: #e5e7eb;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.original-column .paragraph-item:hover {
  border-color: #d1d5db;
  background-color: #f9fafb !important;
}

.translation-column .paragraph-item {
  border-color: #dbeafe;
}

.translation-column .paragraph-item:hover {
  border-color: #93c5fd;
}

/* 滚动条样式 */
.original-column::-webkit-scrollbar,
.translation-column::-webkit-scrollbar {
  width: 6px;
}

.original-column::-webkit-scrollbar-track,
.translation-column::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.original-column::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.translation-column::-webkit-scrollbar-thumb {
  background: #93c5fd;
  border-radius: 3px;
}

.original-column::-webkit-scrollbar-thumb:hover,
.translation-column::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .immersive-content {
    flex-direction: column;
  }

  .original-column,
  .translation-column {
    width: 100% !important;
    height: 50%;
  }

  .original-column {
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .translation-popup .bg-white {
    background-color: #1f2937;
    color: #f9fafb;
  }
  
  .translation-popup .border-gray-200 {
    border-color: #374151;
  }
  
  .translation-popup .text-gray-800 {
    color: #f9fafb;
  }
  
  .translation-popup .bg-gray-50 {
    background-color: #374151;
  }
  
  .translation-popup .bg-blue-50 {
    background-color: #1e3a8a;
  }
}
