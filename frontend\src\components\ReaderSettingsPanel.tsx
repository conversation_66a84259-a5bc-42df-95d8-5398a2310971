'use client'

import { useState } from 'react'
import { useReaderSettings } from '@/hooks/useReaderSettings'
import { 
  Settings, X, Type, Palette, Monitor, Eye, RotateCcw,
  Minus, Plus, AlignLeft, AlignCenter, AlignJustify,
  Sun, Moon, Leaf, Zap
} from 'lucide-react'

interface ReaderSettingsPanelProps {
  isOpen: boolean
  onClose: () => void
}

export default function ReaderSettingsPanel({ isOpen, onClose }: ReaderSettingsPanelProps) {
  const {
    settings,
    tempSettings,
    loading,
    saving,
    updateTempSetting,
    saveTempSettings,
    cancelTempSettings,
    resetSettings,
    applyTheme
  } = useReaderSettings()
  const [activeTab, setActiveTab] = useState<'font' | 'theme' | 'layout' | 'behavior'>('font')

  // 使用临时设置进行显示
  const currentSettings = tempSettings || settings

  if (!isOpen || !currentSettings) return null

  const handleFontSizeChange = (delta: number) => {
    const newSize = Math.max(12, Math.min(32, currentSettings.font_size + delta))
    updateTempSetting('font_size', newSize)
  }

  const handleLineHeightChange = (delta: number) => {
    const newHeight = Math.max(1.0, Math.min(3.0, currentSettings.line_height + delta))
    updateTempSetting('line_height', Math.round(newHeight * 10) / 10)
  }

  // 处理保存
  const handleSave = async () => {
    const success = await saveTempSettings()
    if (success) {
      // 确保主题立即应用
      setTimeout(() => {
        applyTheme()
      }, 50)
      onClose()
    }
  }

  // 处理取消
  const handleCancel = () => {
    cancelTempSettings()
    onClose()
  }

  const tabs = [
    { id: 'font', label: '字体', icon: Type },
    { id: 'theme', label: '主题', icon: Palette },
    { id: 'layout', label: '布局', icon: Monitor },
    { id: 'behavior', label: '行为', icon: Eye },
  ]

  const themes = [
    { id: 'light', label: '浅色', icon: Sun, bg: 'bg-white', text: 'text-gray-900', preview: '#ffffff' },
    { id: 'dark', label: '深色', icon: Moon, bg: 'bg-gray-900', text: 'text-white', preview: '#1a202c' },
    { id: 'sepia', label: '护眼', icon: Leaf, bg: 'bg-amber-50', text: 'text-amber-900', preview: '#f7f3e9' },
    { id: 'night', label: '夜间', icon: Zap, bg: 'bg-slate-900', text: 'text-slate-100', preview: '#0f172a' },
    { id: 'warm', label: '温暖', icon: Sun, bg: 'bg-orange-50', text: 'text-orange-900', preview: '#fefcf8' },
    { id: 'cool', label: '冷色', icon: Zap, bg: 'bg-blue-50', text: 'text-blue-900', preview: '#f8fafc' },
    { id: 'green', label: '绿色', icon: Leaf, bg: 'bg-green-50', text: 'text-green-900', preview: '#f0fdf4' },
    { id: 'purple', label: '紫色', icon: Eye, bg: 'bg-purple-50', text: 'text-purple-900', preview: '#faf7ff' },
  ]

  const fontFamilies = [
    { id: 'system', label: '系统默认' },
    { id: 'serif', label: '宋体' },
    { id: 'sans-serif', label: '黑体' },
    { id: 'monospace', label: '等宽字体' },
  ]

  const textAligns = [
    { id: 'left', label: '左对齐', icon: AlignLeft },
    { id: 'justify', label: '两端对齐', icon: AlignJustify },
    { id: 'center', label: '居中对齐', icon: AlignCenter },
  ]

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-primary-600" />
            <h2 className="text-lg font-semibold text-gray-900">阅读器设置</h2>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={resetSettings}
              disabled={saving}
              className="flex items-center space-x-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 disabled:opacity-50"
            >
              <RotateCcw className="h-4 w-4" />
              <span>重置</span>
            </button>
            <button
              onClick={handleCancel}
              className="p-1 text-gray-400 hover:text-gray-600 rounded-md"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* 标签页 */}
        <div className="flex border-b border-gray-200">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </div>

        {/* 内容区域 */}
        <div className="p-6 max-h-96 overflow-y-auto">
          {/* 字体设置 */}
          {activeTab === 'font' && (
            <div className="space-y-6">
              {/* 字体大小 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  字体大小: {currentSettings.font_size}px
                </label>
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => handleFontSizeChange(-1)}
                    disabled={currentSettings.font_size <= 12}
                    className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                  >
                    <Minus className="h-4 w-4" />
                  </button>
                  <div className="flex-1">
                    <input
                      type="range"
                      min="12"
                      max="32"
                      value={currentSettings.font_size}
                      onChange={(e) => updateTempSetting('font_size', parseInt(e.target.value))}
                      className="w-full"
                    />
                  </div>
                  <button
                    onClick={() => handleFontSizeChange(1)}
                    disabled={currentSettings.font_size >= 32}
                    className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* 行间距 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  行间距: {currentSettings.line_height}
                </label>
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => handleLineHeightChange(-0.1)}
                    disabled={currentSettings.line_height <= 1.0}
                    className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                  >
                    <Minus className="h-4 w-4" />
                  </button>
                  <div className="flex-1">
                    <input
                      type="range"
                      min="1.0"
                      max="3.0"
                      step="0.1"
                      value={currentSettings.line_height}
                      onChange={(e) => updateTempSetting('line_height', parseFloat(e.target.value))}
                      className="w-full"
                    />
                  </div>
                  <button
                    onClick={() => handleLineHeightChange(0.1)}
                    disabled={currentSettings.line_height >= 3.0}
                    className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* 字体族 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">字体族</label>
                <div className="grid grid-cols-2 gap-2">
                  {fontFamilies.map((font) => (
                    <button
                      key={font.id}
                      onClick={() => updateTempSetting('font_family', font.id as any)}
                      className={`p-3 text-left border rounded-md transition-colors ${
                        currentSettings.font_family === font.id
                          ? 'border-primary-500 bg-primary-50 text-primary-700'
                          : 'border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {font.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 主题设置 - 增强版 */}
          {activeTab === 'theme' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-4">选择主题</label>
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
                  {themes.map((theme) => {
                    const Icon = theme.icon
                    const isActive = currentSettings.theme === theme.id
                    return (
                      <button
                        key={theme.id}
                        onClick={() => updateTempSetting('theme', theme.id as any)}
                        className={`group relative p-4 border rounded-xl transition-all duration-300 hover:scale-105 ${
                          isActive
                            ? 'border-blue-500 ring-2 ring-blue-200 shadow-lg'
                            : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                        }`}
                      >
                        {/* 主题预览 */}
                        <div 
                          className="w-full h-16 rounded-lg mb-3 border-2 relative overflow-hidden"
                          style={{ backgroundColor: theme.preview }}
                        >
                          <div className="absolute inset-0 p-2">
                            <div className={`w-full h-2 ${theme.bg} rounded opacity-80 mb-1`}></div>
                            <div className={`w-3/4 h-1.5 ${theme.text.replace('text-', 'bg-')} rounded opacity-60 mb-1`}></div>
                            <div className={`w-1/2 h-1.5 ${theme.text.replace('text-', 'bg-')} rounded opacity-40`}></div>
                          </div>
                        </div>
                        
                        {/* 主题信息 */}
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-2 mb-1">
                            <Icon className="h-4 w-4 text-gray-600" />
                            <span className="text-sm font-medium text-gray-900">{theme.label}</span>
                          </div>
                          {isActive && (
                            <div className="text-xs text-blue-600 font-medium">已选中</div>
                          )}
                        </div>
                        
                        {/* 选中指示器 */}
                        {isActive && (
                          <div className="absolute -top-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}
                      </button>
                    )
                  })}
                </div>
              </div>
              
              {/* 主题预览 */}
              <div className="p-4 border border-gray-200 rounded-xl">
                <h4 className="text-sm font-medium text-gray-700 mb-3">预览效果</h4>
                <div 
                  className="p-4 rounded-lg border transition-all duration-300"
                  style={{
                    backgroundColor: themes.find(t => t.id === currentSettings.theme)?.preview || '#ffffff',
                    color: currentSettings.theme === 'dark' || currentSettings.theme === 'night' ? '#f7fafc' : '#1a202c'
                  }}
                >
                  <h5 className="font-semibold mb-2" style={{ fontSize: `${currentSettings.font_size}px` }}>章节标题示例</h5>
                  <p 
                    style={{ 
                      fontSize: `${currentSettings.font_size}px`, 
                      lineHeight: currentSettings.line_height,
                      textAlign: currentSettings.text_align as any
                    }}
                  >
                    这里是阅读内容的示例文本，您可以预览当前设置的效果。字体大小、行间距和对齐方式都会在这里实时反映。
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* 布局设置 */}
          {activeTab === 'layout' && (
            <div className="space-y-6">
              {/* 页面宽度 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  页面宽度: {currentSettings.page_width}px
                </label>
                <input
                  type="range"
                  min="600"
                  max="1200"
                  value={currentSettings.page_width}
                  onChange={(e) => updateTempSetting('page_width', parseInt(e.target.value))}
                  className="w-full"
                />
              </div>

              {/* 文本对齐 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">文本对齐</label>
                <div className="flex space-x-2">
                  {textAligns.map((align) => {
                    const Icon = align.icon
                    return (
                      <button
                        key={align.id}
                        onClick={() => updateTempSetting('text_align', align.id as any)}
                        className={`flex-1 flex items-center justify-center space-x-2 p-3 border rounded-md transition-colors ${
                          currentSettings.text_align === align.id
                            ? 'border-primary-500 bg-primary-50 text-primary-700'
                            : 'border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        <Icon className="h-4 w-4" />
                        <span className="text-sm">{align.label}</span>
                      </button>
                    )
                  })}
                </div>
              </div>
            </div>
          )}

          {/* 行为设置 */}
          {activeTab === 'behavior' && (
            <div className="space-y-6">
              {/* 显示进度 */}
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">显示阅读进度</label>
                <button
                  onClick={() => updateTempSetting('show_progress', !currentSettings.show_progress)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    currentSettings.show_progress ? 'bg-primary-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      currentSettings.show_progress ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* 全屏模式 */}
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">默认全屏阅读</label>
                <button
                  onClick={() => updateTempSetting('full_screen', !currentSettings.full_screen)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    currentSettings.full_screen ? 'bg-primary-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      currentSettings.full_screen ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* 自动滚动 */}
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">自动滚动</label>
                <button
                  onClick={() => updateTempSetting('auto_scroll', !currentSettings.auto_scroll)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    currentSettings.auto_scroll ? 'bg-primary-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      currentSettings.auto_scroll ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* 滚动速度 */}
              {currentSettings.auto_scroll && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    滚动速度: {currentSettings.scroll_speed}
                  </label>
                  <input
                    type="range"
                    min="10"
                    max="200"
                    value={currentSettings.scroll_speed}
                    onChange={(e) => updateTempSetting('scroll_speed', parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>
              )}
            </div>
          )}
        </div>

        {/* 底部 */}
        <div className="flex justify-between p-6 border-t border-gray-200">
          <button
            onClick={handleCancel}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            disabled={saving}
            className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors disabled:opacity-50"
          >
            {saving ? '保存中...' : '保存设置'}
          </button>
        </div>
      </div>
    </div>
  )
}
