'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { translationAPI } from '@/lib/translationAPI'
import { ImmersiveTranslation } from '@/components/Translation'

export default function ImmersiveTestPage() {
  const { user } = useAuth()
  const [translationSettings, setTranslationSettings] = useState<any>(null)
  const [showImmersive, setShowImmersive] = useState(false)
  const [testContent] = useState(`这是第一段测试内容。沉浸式翻译功能允许用户同时查看原文和译文，提供更好的学习体验。

这是第二段测试内容。我们将测试段落分割和翻译功能是否正常工作。用户可以点击任意段落进行翻译。

This is the third paragraph in English. We want to test if the immersive translation can handle mixed languages correctly.

这是最后一段中文内容。测试完成后，我们应该能看到完整的沉浸式翻译界面。`)

  // 加载翻译设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const response = await translationAPI.getSettings()
        console.log('测试页面 - 翻译设置响应:', response)
        
        if (response.success || response.default_target_language) {
          const settings = response.data || response
          setTranslationSettings(settings)
          console.log('测试页面 - 翻译设置已加载:', settings)
        } else {
          const defaultSettings = {
            default_source_language: 'auto',
            default_target_language: 'zh-CN',
            immersive_enabled: true,
            immersive_auto_translate: false,
            immersive_split_ratio: 0.5
          }
          setTranslationSettings(defaultSettings)
          console.log('测试页面 - 使用默认设置:', defaultSettings)
        }
      } catch (error) {
        console.error('测试页面 - 加载翻译设置失败:', error)
        const defaultSettings = {
          default_source_language: 'auto',
          default_target_language: 'zh-CN',
          immersive_enabled: true,
          immersive_auto_translate: false,
          immersive_split_ratio: 0.5
        }
        setTranslationSettings(defaultSettings)
      }
    }

    if (user) {
      loadSettings()
    }
  }, [user])

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <a href="/auth/login" className="text-blue-500 hover:underline">
            去登录
          </a>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h1 className="text-2xl font-bold mb-4">🌍 沉浸式翻译功能测试</h1>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">调试信息</h3>
            <div className="bg-gray-100 p-4 rounded text-sm">
              <div><strong>用户:</strong> {user.username}</div>
              <div><strong>翻译设置已加载:</strong> {translationSettings ? '✅' : '❌'}</div>
              <div><strong>沉浸式翻译状态:</strong> {showImmersive ? '开启' : '关闭'}</div>
              {translationSettings && (
                <div className="mt-2">
                  <strong>设置详情:</strong>
                  <pre className="mt-1 text-xs">
                    {JSON.stringify(translationSettings, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <button
              onClick={() => {
                console.log('点击沉浸式翻译按钮')
                setShowImmersive(!showImmersive)
              }}
              className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
                showImmersive
                  ? 'bg-red-500 text-white hover:bg-red-600'
                  : 'bg-blue-500 text-white hover:bg-blue-600'
              }`}
            >
              {showImmersive ? '🚫 关闭沉浸式翻译' : '🌍 开启沉浸式翻译'}
            </button>

            {!showImmersive && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-semibold mb-2">测试内容预览</h3>
                <div className="whitespace-pre-wrap text-gray-700">
                  {testContent}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 沉浸式翻译组件 */}
      {showImmersive && translationSettings && (
        <ImmersiveTranslation
          enabled={showImmersive}
          content={testContent}
          bookId="test-book-id"
          chapterId="test-chapter-id"
          splitRatio={translationSettings.immersive_split_ratio || 0.5}
          autoTranslate={translationSettings.immersive_auto_translate || false}
          targetLanguage={translationSettings.default_target_language || 'zh-CN'}
          sourceLanguage={translationSettings.default_source_language || 'auto'}
          onToggle={() => {
            console.log('沉浸式翻译组件内部切换')
            setShowImmersive(false)
          }}
        />
      )}
    </div>
  )
}
