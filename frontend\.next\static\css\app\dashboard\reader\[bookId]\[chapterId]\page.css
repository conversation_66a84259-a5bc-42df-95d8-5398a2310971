/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./src/components/Translation/translation.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
/* 翻译组件通用样式 */

/* 滑块样式 */
.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
          appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 翻译弹窗动画 */
.translation-popup {
  animation: fadeInUp 0.2s ease-out;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 - 增强版 */
@media (max-width: 640px) {
  .translation-popup {
    max-width: 95vw !important;
    min-width: 300px !important;
    margin: 0 10px;
    transform: translateX(-50%) translateY(-50%);
    left: 50% !important;
    top: 50% !important;
  }
  
  .translation-popup .bg-white {
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .translation-popup {
    max-width: 98vw !important;
    min-width: 280px !important;
    margin: 0 5px;
  }
  
  .translation-popup .p-4 {
    padding: 16px 12px;
  }
  
  .translation-popup .text-sm {
    font-size: 13px;
  }
}

/* 沉浸式翻译样式 */
.immersive-translation-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.immersive-controls {
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.immersive-content {
  flex: 1;
  overflow: hidden;
}

.original-column,
.translation-column {
  height: 100%;
}

.paragraph-item {
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.paragraph-item:hover {
  border-color: #e5e7eb;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.original-column .paragraph-item:hover {
  border-color: #d1d5db;
  background-color: #f9fafb !important;
}

.translation-column .paragraph-item {
  border-color: #dbeafe;
}

.translation-column .paragraph-item:hover {
  border-color: #93c5fd;
}

/* 滚动条样式 */
.original-column::-webkit-scrollbar,
.translation-column::-webkit-scrollbar {
  width: 6px;
}

.original-column::-webkit-scrollbar-track,
.translation-column::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.original-column::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.translation-column::-webkit-scrollbar-thumb {
  background: #93c5fd;
  border-radius: 3px;
}

.original-column::-webkit-scrollbar-thumb:hover,
.translation-column::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .immersive-content {
    flex-direction: column;
  }

  .original-column,
  .translation-column {
    width: 100% !important;
    height: 50%;
  }

  .original-column {
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .translation-popup .bg-white {
    background-color: #1f2937;
    color: #f9fafb;
  }
  
  .translation-popup .border-gray-200 {
    border-color: #374151;
  }
  
  .translation-popup .text-gray-800 {
    color: #f9fafb;
  }
  
  .translation-popup .bg-gray-50 {
    background-color: #374151;
  }
  
  .translation-popup .bg-blue-50 {
    background-color: #1e3a8a;
  }
}

/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./src/styles/resizable-sidebar.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/* 章节列表可拖拽调整大小样式 */

/* 拖拽时的全局样式 */
.resizing {
  cursor: col-resize !important;
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
          user-select: none !important;
}

.resizing * {
  cursor: col-resize !important;
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
          user-select: none !important;
}

/* 侧边栏拖拽调整器样式 */
.sidebar-resizer {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  cursor: col-resize;
  z-index: 50;
  transition: all 0.2s ease;
}

.sidebar-resizer:hover {
  background: rgba(59, 130, 246, 0.2);
}

.sidebar-resizer.resizing {
  background: rgba(59, 130, 246, 0.4);
}

/* 拖拽指示器 */
.resize-indicator {
  position: absolute;
  right: -2px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 40px;
  background: linear-gradient(to right, rgba(156, 163, 175, 0.6), rgba(59, 130, 246, 0.6));
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.sidebar-resizer:hover .resize-indicator {
  opacity: 1;
  background: linear-gradient(to right, rgba(59, 130, 246, 0.8), rgba(37, 99, 235, 0.8));
}

.sidebar-resizer.resizing .resize-indicator {
  opacity: 1;
  background: linear-gradient(to right, rgba(37, 99, 235, 0.9), rgba(29, 78, 216, 0.9));
  transform: translateY(-50%) scale(1.1);
}

/* 宽度提示框 */
.width-tooltip {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(17, 24, 39, 0.95);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(55, 65, 81, 0.5);
  pointer-events: none;
  z-index: 60;
}

.width-tooltip::after {
  content: '';
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 6px solid transparent;
  border-left-color: rgba(17, 24, 39, 0.95);
}

/* 平滑过渡 */
.sidebar-transition {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.content-transition {
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-resizer {
    display: none;
  }
}

/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./src/styles/draggable-panel.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/* 可拖动面板样式 */
.draggable-panel {
  transition: all 0.2s ease-out;
}

.draggable-panel.dragging {
  transform: scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  z-index: 9999;
}

.draggable-panel:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 拖动手柄样式 */
.drag-handle {
  position: relative;
}

.drag-handle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.drag-handle:hover::before {
  opacity: 1;
}

/* 调整大小手柄样式 */
.resize-handle {
  position: relative;
  overflow: hidden;
}

.resize-handle::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 16px 16px 0;
  border-color: transparent rgba(59, 130, 246, 0.1) transparent transparent;
  transition: border-color 0.2s ease;
}

.resize-handle:hover::before {
  border-color: transparent rgba(59, 130, 246, 0.2) transparent transparent;
}

/* 面板动画 */
@keyframes panelSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.panel-enter {
  animation: panelSlideIn 0.3s ease-out;
}

/* 防止文本选择 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

/* 拖动时的全局样式 */
body.dragging {
  cursor: grabbing !important;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}

body.dragging * {
  cursor: grabbing !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .draggable-panel {
    max-width: 90vw;
    max-height: 80vh;
  }
}

/* 阴影层级 */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
}

