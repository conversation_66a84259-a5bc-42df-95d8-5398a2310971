"""
AI服务配置管理
"""

from django.conf import settings
from typing import Dict, Any


class AIConfig:
    """AI配置管理器"""
    
    @classmethod
    def get_openai_config(cls) -> Dict[str, Any]:
        """获取OpenAI配置"""
        return {
            'api_key': getattr(settings, 'OPENAI_API_KEY', ''),
            'base_url': getattr(settings, 'OPENAI_BASE_URL', 'https://api.openai.com/v1'),
            'model': getattr(settings, 'OPENAI_MODEL', 'gpt-4o-mini'),
            'timeout': getattr(settings, 'OPENAI_TIMEOUT', 30),
            'max_retries': getattr(settings, 'OPENAI_MAX_RETRIES', 3),
            'mock_mode': getattr(settings, 'AI_MOCK_MODE', False),
        }
    
    @classmethod
    def get_chapter_detection_config(cls) -> Dict[str, Any]:
        """获取章节检测配置"""
        return {
            'max_text_length': getattr(settings, 'AI_CHAPTER_MAX_TEXT_LENGTH', 100000),
            'segment_length': getattr(settings, 'AI_CHAPTER_SEGMENT_LENGTH', 50000),
            'segment_overlap': getattr(settings, 'AI_CHAPTER_SEGMENT_OVERLAP', 2000),
            'min_chapter_length': getattr(settings, 'AI_CHAPTER_MIN_LENGTH', 1000),
            'max_chapter_length': getattr(settings, 'AI_CHAPTER_MAX_LENGTH', 15000),
            'target_chapter_length': getattr(settings, 'AI_CHAPTER_TARGET_LENGTH', 5000),
            'min_chapters': getattr(settings, 'AI_CHAPTER_MIN_COUNT', 3),
            'temperature': getattr(settings, 'AI_CHAPTER_TEMPERATURE', 0.1),
            'max_tokens': getattr(settings, 'AI_CHAPTER_MAX_TOKENS', 2000),
        }
    
    @classmethod
    def is_ai_enabled(cls) -> bool:
        """检查AI功能是否启用"""
        return bool(getattr(settings, 'OPENAI_API_KEY', ''))
    
    @classmethod
    def get_prompt_config(cls) -> Dict[str, str]:
        """获取提示词配置"""
        return {
            'chapter_detection': getattr(
                settings, 
                'AI_CHAPTER_DETECTION_PROMPT',
                cls._get_default_chapter_detection_prompt()
            ),
        }
    
    @classmethod
    def _get_default_chapter_detection_prompt(cls) -> str:
        """获取默认章节检测提示词"""
        return """你是一个专业的文本分析助手，擅长识别小说和长文本中的自然章节分割点。

请分析以下文本，识别出合理的章节分割位置。分析时请考虑：

1. **内容语义**：情节转折点、场景变化、时间跳跃
2. **段落结构**：自然的段落分组和逻辑断点
3. **长度均衡**：每章长度尽量在2000-8000字符之间
4. **故事完整性**：保持每章内容的相对完整性

文本内容：
{text}

请以JSON格式返回章节分割结果，格式如下：
{{
    "chapters": [
        {{
            "title": "第一章",
            "start_position": 0,
            "end_position": 1500,
            "reason": "开篇介绍，场景设定完整"
        }},
        {{
            "title": "第二章", 
            "start_position": 1500,
            "end_position": 3200,
            "reason": "情节发展，人物关系变化"
        }}
    ]
}}

要求：
- 至少分割出3个章节
- 每章标题简洁明了
- 分割位置准确到字符位置
- 提供分割理由
- 确保位置连续，无遗漏或重叠"""
