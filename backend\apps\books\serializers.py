"""
书籍相关序列化器
"""

from rest_framework import serializers
from django.core.files.uploadedfile import UploadedFile
from django.contrib.auth import get_user_model
from .models import (
    Book, Chapter, ReadingProgress, Bookmark, ChapterSummary, ReaderSettings,
    TranslationCache, TranslationHistory, TranslationSettings
)

User = get_user_model()


class BookUploadSerializer(serializers.Serializer):
    """书籍上传序列化器"""
    
    file = serializers.FileField(
        help_text='TXT 格式的电子书文件',
        required=True
    )
    title = serializers.CharField(
        max_length=200,
        required=False,
        help_text='书籍标题（可选，如果不提供将从文件名提取）'
    )
    author = serializers.CharField(
        max_length=100,
        required=False,
        help_text='作者（可选）'
    )
    description = serializers.CharField(
        required=False,
        help_text='书籍描述（可选）'
    )
    
    def validate_file(self, value):
        """验证上传的文件"""
        if not value:
            raise serializers.ValidationError('请选择要上传的文件')
        
        # 检查文件大小 (最大 50MB)
        max_size = 50 * 1024 * 1024  # 50MB
        if value.size > max_size:
            raise serializers.ValidationError('文件大小不能超过 50MB')
        
        # 检查文件扩展名
        allowed_extensions = ['.txt']
        file_name = value.name.lower()
        if not any(file_name.endswith(ext) for ext in allowed_extensions):
            raise serializers.ValidationError('只支持 TXT 格式的文件')
        
        # 检查文件内容类型
        if value.content_type and not value.content_type.startswith('text/'):
            # 允许一些常见的文本文件 MIME 类型
            allowed_types = [
                'text/plain',
                'text/txt',
                'application/octet-stream',  # 有时 TXT 文件会被识别为这个类型
            ]
            if value.content_type not in allowed_types:
                raise serializers.ValidationError('文件类型不正确，请上传 TXT 文件')
        
        return value
    
    def validate_title(self, value):
        """验证书籍标题"""
        if value and len(value.strip()) == 0:
            raise serializers.ValidationError('书籍标题不能为空')
        return value.strip() if value else value


class ChapterSerializer(serializers.ModelSerializer):
    """章节序列化器"""
    
    class Meta:
        model = Chapter
        fields = [
            'id', 'title', 'order_index', 'content_preview',
            'word_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    content_preview = serializers.SerializerMethodField()
    
    def get_content_preview(self, obj):
        """获取章节内容预览（前200个字符）"""
        try:
            # 从文件中获取章节内容
            from .views import _get_chapter_content_from_file
            content = _get_chapter_content_from_file(obj.book, obj)
            if content and len(content) > 200:
                return content[:200] + '...'
            return content or ''
        except Exception:
            return f"第{obj.order_index}章内容预览"


class BookListSerializer(serializers.ModelSerializer):
    """书籍列表序列化器"""

    total_chapters = serializers.IntegerField(source='chapter_count', read_only=True)
    uploaded_at = serializers.DateTimeField(source='created_at', read_only=True)

    class Meta:
        model = Book
        fields = [
            'id', 'title', 'author', 'filename', 'status',
            'total_chapters', 'total_words', 'file_size',
            'uploaded_at', 'updated_at'
        ]
        read_only_fields = ['id', 'uploaded_at', 'updated_at']


class BookDetailSerializer(serializers.ModelSerializer):
    """书籍详情序列化器"""

    chapters = ChapterSerializer(many=True, read_only=True)
    reading_progress = serializers.SerializerMethodField()
    total_chapters = serializers.IntegerField(source='chapter_count', read_only=True)
    uploaded_at = serializers.DateTimeField(source='created_at', read_only=True)

    class Meta:
        model = Book
        fields = [
            'id', 'title', 'author', 'filename', 'status',
            'total_chapters', 'total_words', 'file_size',
            'uploaded_at', 'updated_at', 'chapters', 'reading_progress'
        ]
        read_only_fields = ['id', 'uploaded_at', 'updated_at']
    
    def get_reading_progress(self, obj):
        """获取当前用户的阅读进度"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                progress = ReadingProgress.objects.get(
                    user=request.user,
                    book=obj
                )
                # 根据章节索引找到对应的章节ID
                current_chapter_id = None
                if progress.current_chapter > 0:
                    try:
                        chapter = obj.chapters.get(order_index=progress.current_chapter)
                        current_chapter_id = chapter.id
                    except Chapter.DoesNotExist:
                        pass

                return {
                    'current_chapter': current_chapter_id,
                    'current_position': progress.current_position,
                    'progress_percentage': float(progress.percentage),
                    'last_read_at': progress.last_read_at
                }
            except ReadingProgress.DoesNotExist:
                pass
        return None


class ReadingProgressSerializer(serializers.ModelSerializer):
    """阅读进度序列化器"""
    
    class Meta:
        model = ReadingProgress
        fields = [
            'id', 'book', 'current_chapter', 'current_position',
            'progress_percentage', 'last_read_at', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class BookmarkSerializer(serializers.ModelSerializer):
    """书签序列化器"""

    chapter_title = serializers.CharField(source='chapter.title', read_only=True)
    chapter_order = serializers.IntegerField(source='chapter.order_index', read_only=True)
    book_title = serializers.CharField(source='book.title', read_only=True)
    context_preview = serializers.SerializerMethodField()

    class Meta:
        model = Bookmark
        fields = [
            'id', 'user', 'book', 'chapter', 'position', 'scroll_position',
            'title', 'note', 'type', 'color', 'is_private',
            'context_before', 'context_after', 'context_preview',
            'chapter_title', 'chapter_order', 'book_title',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user', 'created_at', 'updated_at', 'context_preview']

    def get_context_preview(self, obj):
        """获取书签上下文预览"""
        return obj.get_context_preview()

    def validate_position(self, value):
        """验证位置参数"""
        if value < 0:
            raise serializers.ValidationError("位置不能为负数")
        return value

    def validate_scroll_position(self, value):
        """验证滚动位置参数"""
        if value < 0:
            raise serializers.ValidationError("滚动位置不能为负数")
        return value


class ChapterSummarySerializer(serializers.ModelSerializer):
    """章节总结序列化器"""
    
    class Meta:
        model = ChapterSummary
        fields = [
            'id', 'book', 'chapter', 'summary', 'key_points',
            'generated_at', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class ReaderSettingsSerializer(serializers.ModelSerializer):
    """阅读器设置序列化器"""

    class Meta:
        model = ReaderSettings
        fields = [
            'user', 'font_size', 'line_height', 'font_family', 'theme',
            'page_width', 'text_align', 'auto_scroll', 'scroll_speed',
            'show_progress', 'full_screen', 'created_at', 'updated_at'
        ]
        read_only_fields = ['user', 'created_at', 'updated_at']

    def validate_font_size(self, value):
        """验证字体大小"""
        if not 12 <= value <= 32:
            raise serializers.ValidationError("字体大小必须在 12-32px 之间")
        return value

    def validate_line_height(self, value):
        """验证行间距"""
        if not 1.0 <= float(value) <= 3.0:
            raise serializers.ValidationError("行间距必须在 1.0-3.0 之间")
        return value

    def validate_page_width(self, value):
        """验证页面宽度"""
        if not 600 <= value <= 1200:
            raise serializers.ValidationError("页面宽度必须在 600-1200px 之间")
        return value

    def validate_scroll_speed(self, value):
        """验证滚动速度"""
        if not 10 <= value <= 200:
            raise serializers.ValidationError("滚动速度必须在 10-200 之间")
        return value


class TranslationRequestSerializer(serializers.Serializer):
    """翻译请求序列化器"""

    text = serializers.CharField(max_length=5000, help_text='要翻译的文本')
    target_language = serializers.CharField(max_length=10, default='zh-CN', help_text='目标语言')
    source_language = serializers.CharField(max_length=10, default='auto', help_text='源语言')
    context = serializers.CharField(max_length=1000, required=False, allow_blank=True, help_text='上下文信息')
    book_id = serializers.UUIDField(required=False, help_text='书籍ID')
    chapter_id = serializers.UUIDField(required=False, help_text='章节ID')
    position = serializers.IntegerField(required=False, help_text='文本在章节中的位置')

    def validate_text(self, value):
        """验证翻译文本"""
        if not value.strip():
            raise serializers.ValidationError("翻译文本不能为空")
        if len(value.strip()) < 2:
            raise serializers.ValidationError("翻译文本至少需要2个字符")
        return value.strip()


class TranslationResponseSerializer(serializers.Serializer):
    """翻译响应序列化器"""

    success = serializers.BooleanField()
    translation = serializers.CharField(required=False)
    original_text = serializers.CharField()
    detected_language = serializers.CharField(required=False)
    target_language = serializers.CharField()
    confidence = serializers.FloatField(required=False)
    error = serializers.CharField(required=False)
    cached = serializers.BooleanField(default=False)


class TranslationHistorySerializer(serializers.ModelSerializer):
    """翻译历史序列化器"""

    book_title = serializers.CharField(source='book.title', read_only=True)
    chapter_title = serializers.CharField(source='chapter.title', read_only=True)

    class Meta:
        model = TranslationHistory
        fields = [
            'id', 'original_text', 'translated_text', 'source_language',
            'target_language', 'confidence', 'is_favorited', 'user_rating',
            'notes', 'book_title', 'chapter_title', 'position_in_chapter',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class TranslationSettingsSerializer(serializers.ModelSerializer):
    """翻译设置序列化器"""

    class Meta:
        model = TranslationSettings
        fields = [
            'default_source_language', 'default_target_language', 'auto_translate',
            'show_original', 'translation_position', 'immersive_enabled',
            'immersive_auto_translate', 'immersive_split_ratio', 'min_confidence',
            'save_to_history', 'font_size', 'theme', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def validate_font_size(self, value):
        """验证字体大小"""
        if not 10 <= value <= 24:
            raise serializers.ValidationError("字体大小必须在 10-24px 之间")
        return value

    def validate_min_confidence(self, value):
        """验证最低置信度"""
        if not 0.1 <= value <= 1.0:
            raise serializers.ValidationError("置信度必须在 0.1-1.0 之间")
        return value
    
    def validate_immersive_split_ratio(self, value):
        """验证沉浸式翻译分栏比例"""
        if not 0.2 <= value <= 0.8:
            raise serializers.ValidationError("分栏比例必须在 0.2-0.8 之间")
        return value
