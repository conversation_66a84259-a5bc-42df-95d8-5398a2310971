/* 阅读器主题样式 - 增强版 */

/* CSS 变量定义 */
:root {
  /* 默认浅色主题 */
  --reader-bg: #ffffff;
  --reader-text: #1a202c;
  --reader-border: #e5e7eb;
  --reader-secondary: #6b7280;
  --reader-accent: #3b82f6;
  --reader-muted: #f8fafc;
  --reader-highlight: #dbeafe;
  
  /* 默认字体设置 */
  --reader-font-size: 18px;
  --reader-line-height: 1.8;
  --reader-font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Noto Sans SC", sans-serif;
  --reader-page-width: 1200px;
  --reader-text-align: justify;
  
  /* 增强的视觉效果 */
  --reader-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --reader-radius: 16px;
  --reader-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 浅色主题 - 增强版 */
.theme-light {
  --reader-bg: #ffffff;
  --reader-text: #1a202c;
  --reader-border: #e5e7eb;
  --reader-secondary: #6b7280;
  --reader-accent: #3b82f6;
  --reader-muted: #f8fafc;
  --reader-highlight: #dbeafe;
}

/* 深色主题 - 增强版 */
.theme-dark {
  --reader-bg: #1a202c;
  --reader-text: #f7fafc;
  --reader-border: #2d3748;
  --reader-secondary: #a0aec0;
  --reader-accent: #63b3ed;
  --reader-muted: #2d3748;
  --reader-highlight: rgba(99, 179, 237, 0.2);
}

/* 护眼主题 - 增强版 */
.theme-sepia {
  --reader-bg: #f7f3e9;
  --reader-text: #5c4a37;
  --reader-border: #e6dcc6;
  --reader-secondary: #8b7355;
  --reader-accent: #d69e2e;
  --reader-muted: #faf5f0;
  --reader-highlight: rgba(214, 158, 46, 0.2);
}

/* 夜间主题 - 增强版 */
.theme-night {
  --reader-bg: #0f172a;
  --reader-text: #f1f5f9;
  --reader-border: #1e293b;
  --reader-secondary: #64748b;
  --reader-accent: #38bdf8;
  --reader-muted: #1e293b;
  --reader-highlight: rgba(56, 189, 248, 0.2);
}

/* 新增主题 */

/* 温暖主题 */
.theme-warm {
  --reader-bg: #fefcf8;
  --reader-text: #44403c;
  --reader-border: #f3f1eb;
  --reader-secondary: #78716c;
  --reader-accent: #f59e0b;
  --reader-muted: #fdfbf7;
  --reader-highlight: rgba(245, 158, 11, 0.2);
}

/* 冷色主题 */
.theme-cool {
  --reader-bg: #f8fafc;
  --reader-text: #1e293b;
  --reader-border: #e2e8f0;
  --reader-secondary: #64748b;
  --reader-accent: #0ea5e9;
  --reader-muted: #f1f5f9;
  --reader-highlight: rgba(14, 165, 233, 0.2);
}

/* 绿色主题（护眼） */
.theme-green {
  --reader-bg: #f0fdf4;
  --reader-text: #14532d;
  --reader-border: #dcfce7;
  --reader-secondary: #4ade80;
  --reader-accent: #16a34a;
  --reader-muted: #f7fee7;
  --reader-highlight: rgba(22, 163, 74, 0.2);
}

/* 紫色主题 */
.theme-purple {
  --reader-bg: #faf7ff;
  --reader-text: #2d1b69;
  --reader-border: #e9d5ff;
  --reader-secondary: #7c3aed;
  --reader-accent: #8b5cf6;
  --reader-muted: #f5f3ff;
  --reader-highlight: rgba(139, 92, 246, 0.2);
}

/* 高对比主题 */
.theme-contrast {
  --reader-bg: #ffffff;
  --reader-text: #000000;
  --reader-border: #666666;
  --reader-secondary: #333333;
  --reader-accent: #0066cc;
  --reader-muted: #f5f5f5;
  --reader-highlight: rgba(0, 102, 204, 0.3);
}

/* 阅读器容器样式 - 增强版 */
.reader-container {
  background-color: var(--reader-bg);
  color: var(--reader-text);
  transition: var(--reader-transition);
  min-height: 100vh;
  position: relative;
}

/* 阅读内容区域 */
.reader-content {
  font-size: var(--reader-font-size);
  line-height: var(--reader-line-height);
  font-family: var(--reader-font-family);
  text-align: var(--reader-text-align);
  max-width: var(--reader-page-width);
  margin: 0 auto;
  transition: var(--reader-transition);
  position: relative;
}

/* 阅读文章样式 */
.reader-article {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "liga", "kern";
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* 专注模式 */
.focus-mode {
  filter: none;
  transition: var(--reader-transition);
}

.focus-article {
  padding: 4rem 3rem;
  background: var(--reader-muted);
  border-radius: var(--reader-radius);
  box-shadow: var(--reader-shadow);
  margin: 2rem 0;
}

/* 字词高亮效果 */
.word-highlight .chapter-content:hover {
  background: linear-gradient(90deg, transparent 0%, var(--reader-highlight) 50%, transparent 100%);
  background-size: 200% 1.5em;
  background-repeat: no-repeat;
  animation: wordHighlight 2s ease-in-out;
}

@keyframes wordHighlight {
  0%, 100% { background-position: -100% 0; }
  50% { background-position: 100% 0; }
}

/* 行高亮效果 */
.line-highlight .chapter-content {
  background: linear-gradient(
    to bottom,
    transparent 0%,
    transparent calc(50% - 0.75em),
    var(--reader-highlight) calc(50% - 0.75em),
    var(--reader-highlight) calc(50% + 0.75em),
    transparent calc(50% + 0.75em),
    transparent 100%
  );
  background-attachment: local;
}

/* 滚动条样式 */
.reader-content::-webkit-scrollbar {
  width: 8px;
}

.reader-content::-webkit-scrollbar-track {
  background: var(--reader-bg);
}

.reader-content::-webkit-scrollbar-thumb {
  background: var(--reader-secondary);
  border-radius: 4px;
}

.reader-content::-webkit-scrollbar-thumb:hover {
  background: var(--reader-text);
}

/* 选择文本样式 - 增强版 */
.reader-content ::selection {
  background-color: var(--reader-highlight);
  color: var(--reader-text);
}

/* 确保在所有主题下选择样式的可见性 */
.theme-light .reader-content ::selection,
.theme-light .reader-article ::selection {
  background-color: rgba(59, 130, 246, 0.25);
  color: #1a202c;
}

.theme-dark .reader-content ::selection,
.theme-dark .reader-article ::selection {
  background-color: rgba(99, 179, 237, 0.3);
  color: #f7fafc;
}

.theme-sepia .reader-content ::selection,
.theme-sepia .reader-article ::selection {
  background-color: rgba(214, 158, 46, 0.25);
  color: #5c4a37;
}

.theme-night .reader-content ::selection,
.theme-night .reader-article ::selection {
  background-color: rgba(56, 189, 248, 0.3);
  color: #f1f5f9;
}

/* 链接样式 */
.reader-content a {
  color: #3b82f6;
  text-decoration: underline;
}

.theme-dark .reader-content a {
  color: #60a5fa;
}

.theme-sepia .reader-content a {
  color: #d97706;
}

.theme-night .reader-content a {
  color: #38bdf8;
}

/* 代码块样式 */
.reader-content code {
  background-color: var(--reader-border);
  color: var(--reader-text);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', Consolas, monospace;
}

/* 引用样式 */
.reader-content blockquote {
  border-left: 4px solid var(--reader-secondary);
  padding-left: 16px;
  margin: 16px 0;
  font-style: italic;
  color: var(--reader-secondary);
}

/* 分隔线样式 */
.reader-content hr {
  border: none;
  height: 1px;
  background-color: var(--reader-border);
  margin: 24px 0;
}

/* 表格样式 */
.reader-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
}

.reader-content th,
.reader-content td {
  border: 1px solid var(--reader-border);
  padding: 8px 12px;
  text-align: left;
}

.reader-content th {
  background-color: var(--reader-border);
  font-weight: bold;
}

/* 列表样式 */
.reader-content ul,
.reader-content ol {
  padding-left: 24px;
  margin: 16px 0;
}

.reader-content li {
  margin: 4px 0;
}

/* 图片样式 */
.reader-content img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 16px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reader-content {
    padding: 16px;
    font-size: calc(var(--reader-font-size) * 0.9);
  }
  
  :root {
    --reader-page-width: 100%;
  }
}

/* 新增视觉效果 */

/* 焦点模式效果 */
.focus-mode {
  background: radial-gradient(circle at center, var(--reader-bg) 60%, rgba(0,0,0,0.05) 100%);
}

/* 文本选中增强效果 */
.reader-article ::selection {
  background: var(--reader-highlight);
  color: var(--reader-text);
  text-shadow: none;
}

/* 段落样式 - 简洁设计 */
.chapter-content p {
  margin: 1em 0;
  line-height: inherit;
}

/* 滚动条美化 */
.reader-content-wrapper::-webkit-scrollbar {
  width: 12px;
}

.reader-content-wrapper::-webkit-scrollbar-track {
  background: var(--reader-muted);
  border-radius: 8px;
}

.reader-content-wrapper::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, var(--reader-accent), var(--reader-secondary));
  border-radius: 8px;
  border: 2px solid var(--reader-bg);
}

.reader-content-wrapper::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, var(--reader-secondary), var(--reader-accent));
}

/* 加载动画 */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    var(--reader-muted) 0%,
    var(--reader-highlight) 50%,
    var(--reader-muted) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* 页面切换动画 */
.page-transition {
  animation: pageSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes pageSlideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 按钮点击波纹效果 */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.6s, height 0.6s;
  transform: translate(-50%, -50%);
}

.ripple:active::before {
  width: 300px;
  height: 300px;
}

/* 气泡动画 */
@keyframes bubble {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-10px) scale(1.05);
  }
}

.animate-bubble {
  animation: bubble 3s ease-in-out infinite;
}

/* 文字渐现动画 */
.text-reveal {
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--reader-text) 50%,
    transparent 100%
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: textReveal 2s ease-in-out;
}

@keyframes textReveal {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* 工具提示样式 */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--reader-text);
  color: var(--reader-bg);
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s, transform 0.3s;
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
  transform: translateX(-50%) translateY(-0.5rem);
}

@media (max-width: 480px) {
  .reader-content {
    padding: 12px;
    font-size: calc(var(--reader-font-size) * 0.85);
  }
}

/* 打印样式 */
@media print {
  .reader-content {
    background: white !important;
    color: black !important;
    font-size: 12pt;
    line-height: 1.5;
  }
  
  .reader-content a {
    color: black !important;
    text-decoration: none;
  }
  
  .reader-content a:after {
    content: " (" attr(href) ")";
    font-size: 10pt;
    color: #666;
  }
}

/* 动画效果 - 增强版 */
.reader-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.fade-in-scale {
  animation: fadeInScale 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 浮动动画 */
@keyframes float {
  0%, 100% { 
    transform: translateY(0) rotate(0deg); 
  }
  25% { 
    transform: translateY(-3px) rotate(0.5deg); 
  }
  50% { 
    transform: translateY(-6px) rotate(0deg); 
  }
  75% { 
    transform: translateY(-3px) rotate(-0.5deg); 
  }
}

.animate-float {
  animation: float 4s ease-in-out infinite;
}

/* 脉冲动画 */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 var(--reader-accent);
  }
  50% {
    box-shadow: 0 0 20px 5px rgba(59, 130, 246, 0.3);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* 焦点样式 */
.reader-content:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 高对比度模式支持 - 增强版 */
@media (prefers-contrast: high) {
  :root {
    --reader-border: #000000;
    --reader-secondary: #000000;
    --reader-accent: #0000ff;
  }
  
  .theme-dark {
    --reader-border: #ffffff;
    --reader-secondary: #ffffff;
    --reader-accent: #00ffff;
  }
  
  /* 高对比度下的文本选择 */
  .reader-content ::selection,
  .reader-article ::selection {
    background-color: #ffff00 !important;
    color: #000000 !important;
  }
  
  .theme-dark .reader-content ::selection,
  .theme-dark .reader-article ::selection {
    background-color: #ffff00 !important;
    color: #000000 !important;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .reader-content,
  .reader-container {
    transition: none;
  }
  
  .reader-fade-in {
    animation: none;
  }
}

/* 浮动操作按钮样式 - 增强版 */
.floating-actions {
  z-index: 40;
  pointer-events: auto;
}

.floating-button {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.08), 
    0 4px 16px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.floating-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.floating-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.12), 
    0 6px 20px rgba(0, 0, 0, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.floating-button:hover::before {
  left: 100%;
}

.floating-button:active {
  transform: translateY(-1px) scale(0.98);
  box-shadow: 
    0 6px 24px rgba(0, 0, 0, 0.1), 
    0 3px 12px rgba(0, 0, 0, 0.06);
}

/* 浮动按钮在不同主题下的样式 */
.theme-dark .floating-button {
  background: rgba(31, 41, 55, 0.9);
  border-color: rgba(55, 65, 81, 0.5);
  color: #f9fafb;
}

.theme-dark .floating-button:hover {
  background: rgba(55, 65, 81, 0.9);
}

.theme-sepia .floating-button {
  background: rgba(247, 243, 233, 0.9);
  border-color: rgba(230, 220, 198, 0.5);
  color: #5c4a37;
}

.theme-night .floating-button {
  background: rgba(15, 23, 42, 0.9);
  border-color: rgba(30, 41, 59, 0.5);
  color: #e2e8f0;
}

/* 浮动按钮动画效果 */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}

.floating-button.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* 移动端响应式设计 - 增强版 */
@media (max-width: 768px) {
  .reader-content {
    padding: 1rem 0.75rem;
    font-size: calc(var(--reader-font-size) * 0.95);
  }
  
  :root {
    --reader-page-width: 100%;
  }
  
  /* 移动端文本选择优化 */
  .reader-content ::selection,
  .reader-article ::selection {
    background-color: var(--reader-accent) !important;
    color: var(--reader-bg) !important;
    border-radius: 2px;
  }
  
  .reader-article {
    font-size: calc(var(--reader-font-size) * 0.9);
    line-height: 1.7;
  }
  
  /* 浮动按钮适配 */
  .floating-actions {
    right: 4rem;
    bottom: 6rem;
    top: auto;
    transform: none;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    max-width: 120px;
  }
  
  /* 显示/隐藏切换按钮移动端适配 */
  .fixed.right-6.top-1\/2 {
    right: 1rem;
  }
  
  /* 字体调节工具移动端适配 */
  .fixed.bottom-6.right-6.z-40 {
    bottom: 8rem !important; /* 避免与其他按钮重叠 */
    right: 1rem !important;
  }
  
  .fixed.bottom-6.right-6.z-40 > div {
    transform: scale(0.9); /* 缩小尺寸适合小屏幕 */
  }
  
  .floating-button {
    width: 3rem;
    height: 3rem;
    border-radius: 1rem;
  }
  
  .floating-navigation {
    left: 1rem;
    bottom: 6rem;
    top: auto;
    transform: none;
  }
  
  .floating-navigation > div {
    flex-direction: row;
    padding: 0.5rem;
    border-radius: 1rem;
  }
  
  .floating-navigation button {
    width: 3rem;
    height: 3rem;
  }
  
  /* 隐藏工具栏提示 */
  .group:hover .absolute {
    display: none;
  }
}

@media (max-width: 640px) {
  .reader-content {
    padding: 1rem 0.75rem;
    font-size: calc(var(--reader-font-size) * 0.9);
  }
  
  .reader-article {
    font-size: calc(var(--reader-font-size) * 0.85);
    line-height: 1.6;
  }
  
  /* 粉颇化浮动按钮 */
  .floating-actions {
    right: 0.5rem;
    bottom: 5rem;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    max-width: 100px;
  }
  
  .floating-button {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.75rem;
  }
  
  .floating-navigation {
    left: 0.5rem;
    bottom: 5rem;
  }
  
  :root {
    --reader-page-width: 100%;
    --reader-radius: 12px;
  }
}

@media (max-width: 480px) {
  .reader-container {
    font-size: 14px;
  }
  
  .reader-article {
    font-size: calc(var(--reader-font-size) * 0.8);
    padding: 1rem;
  }
  
  /* 隐藏部分浮动按钮 */
  .floating-actions {
    grid-template-columns: 1fr;
    max-width: 60px;
  }
  
  /* 粉颐化浮动导航 */
  .floating-navigation button {
    width: 2.5rem;
    height: 2.5rem;
  }
}
