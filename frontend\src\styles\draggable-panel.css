/* 可拖动面板样式 */
.draggable-panel {
  transition: all 0.2s ease-out;
}

.draggable-panel.dragging {
  transform: scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  z-index: 9999;
}

.draggable-panel:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 拖动手柄样式 */
.drag-handle {
  position: relative;
}

.drag-handle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.drag-handle:hover::before {
  opacity: 1;
}

/* 调整大小手柄样式 */
.resize-handle {
  position: relative;
  overflow: hidden;
}

.resize-handle::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 16px 16px 0;
  border-color: transparent rgba(59, 130, 246, 0.1) transparent transparent;
  transition: border-color 0.2s ease;
}

.resize-handle:hover::before {
  border-color: transparent rgba(59, 130, 246, 0.2) transparent transparent;
}

/* 面板动画 */
@keyframes panelSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.panel-enter {
  animation: panelSlideIn 0.3s ease-out;
}

/* 防止文本选择 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 拖动时的全局样式 */
body.dragging {
  cursor: grabbing !important;
  user-select: none;
}

body.dragging * {
  cursor: grabbing !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .draggable-panel {
    max-width: 90vw;
    max-height: 80vh;
  }
}

/* 阴影层级 */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
}
