{"c": ["app/layout", "app/dashboard/reader/[bookId]/[chapterId]/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/@ungap/structured-clone/esm/deserialize.js", "(app-pages-browser)/./node_modules/@ungap/structured-clone/esm/index.js", "(app-pages-browser)/./node_modules/@ungap/structured-clone/esm/serialize.js", "(app-pages-browser)/./node_modules/@ungap/structured-clone/esm/types.js", "(app-pages-browser)/./node_modules/bail/index.js", "(app-pages-browser)/./node_modules/ccount/index.js", "(app-pages-browser)/./node_modules/comma-separated-tokens/index.js", "(app-pages-browser)/./node_modules/date-fns/_lib/defaultOptions.js", "(app-pages-browser)/./node_modules/date-fns/_lib/getRoundingMethod.js", "(app-pages-browser)/./node_modules/date-fns/_lib/getTimezoneOffsetInMilliseconds.js", "(app-pages-browser)/./node_modules/date-fns/_lib/normalizeDates.js", "(app-pages-browser)/./node_modules/date-fns/compareAsc.js", "(app-pages-browser)/./node_modules/date-fns/constants.js", "(app-pages-browser)/./node_modules/date-fns/constructFrom.js", "(app-pages-browser)/./node_modules/date-fns/constructNow.js", "(app-pages-browser)/./node_modules/date-fns/differenceInCalendarMonths.js", "(app-pages-browser)/./node_modules/date-fns/differenceInMilliseconds.js", "(app-pages-browser)/./node_modules/date-fns/differenceInMonths.js", "(app-pages-browser)/./node_modules/date-fns/differenceInSeconds.js", "(app-pages-browser)/./node_modules/date-fns/endOfDay.js", "(app-pages-browser)/./node_modules/date-fns/endOfMonth.js", "(app-pages-browser)/./node_modules/date-fns/formatDistance.js", "(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js", "(app-pages-browser)/./node_modules/date-fns/isLastDayOfMonth.js", "(app-pages-browser)/./node_modules/date-fns/isSameWeek.js", "(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildFormatLongFn.js", "(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildLocalizeFn.js", "(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchFn.js", "(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchPatternFn.js", "(app-pages-browser)/./node_modules/date-fns/locale/en-US.js", "(app-pages-browser)/./node_modules/date-fns/locale/en-US/_lib/formatDistance.js", "(app-pages-browser)/./node_modules/date-fns/locale/en-US/_lib/formatLong.js", "(app-pages-browser)/./node_modules/date-fns/locale/en-US/_lib/formatRelative.js", "(app-pages-browser)/./node_modules/date-fns/locale/en-US/_lib/localize.js", "(app-pages-browser)/./node_modules/date-fns/locale/en-US/_lib/match.js", "(app-pages-browser)/./node_modules/date-fns/locale/zh-CN.js", "(app-pages-browser)/./node_modules/date-fns/locale/zh-CN/_lib/formatDistance.js", "(app-pages-browser)/./node_modules/date-fns/locale/zh-CN/_lib/formatLong.js", "(app-pages-browser)/./node_modules/date-fns/locale/zh-CN/_lib/formatRelative.js", "(app-pages-browser)/./node_modules/date-fns/locale/zh-CN/_lib/localize.js", "(app-pages-browser)/./node_modules/date-fns/locale/zh-CN/_lib/match.js", "(app-pages-browser)/./node_modules/date-fns/startOfWeek.js", "(app-pages-browser)/./node_modules/date-fns/toDate.js", "(app-pages-browser)/./node_modules/debug/src/browser.js", "(app-pages-browser)/./node_modules/debug/src/common.js", "(app-pages-browser)/./node_modules/decode-named-character-reference/index.dom.js", "(app-pages-browser)/./node_modules/dequal/dist/index.mjs", "(app-pages-browser)/./node_modules/devlop/lib/development.js", "(app-pages-browser)/./node_modules/estree-util-is-identifier-name/lib/index.js", "(app-pages-browser)/./node_modules/extend/index.js", "(app-pages-browser)/./node_modules/hast-util-to-jsx-runtime/lib/index.js", "(app-pages-browser)/./node_modules/hast-util-whitespace/lib/index.js", "(app-pages-browser)/./node_modules/html-url-attributes/lib/index.js", "(app-pages-browser)/./node_modules/inline-style-parser/index.js", "(app-pages-browser)/./node_modules/is-plain-obj/index.js", "(app-pages-browser)/./node_modules/longest-streak/index.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/align-center.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/align-justify.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/align-left.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bookmark-check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bookmark-plus.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coffee.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/languages.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/leaf.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize-2.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer-2.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-left.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-line.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js", "(app-pages-browser)/./node_modules/markdown-table/index.js", "(app-pages-browser)/./node_modules/mdast-util-find-and-replace/lib/index.js", "(app-pages-browser)/./node_modules/mdast-util-find-and-replace/node_modules/escape-string-regexp/index.js", "(app-pages-browser)/./node_modules/mdast-util-from-markdown/dev/lib/index.js", "(app-pages-browser)/./node_modules/mdast-util-gfm-autolink-literal/lib/index.js", "(app-pages-browser)/./node_modules/mdast-util-gfm-footnote/lib/index.js", "(app-pages-browser)/./node_modules/mdast-util-gfm-strikethrough/lib/index.js", "(app-pages-browser)/./node_modules/mdast-util-gfm-table/lib/index.js", "(app-pages-browser)/./node_modules/mdast-util-gfm-task-list-item/lib/index.js", "(app-pages-browser)/./node_modules/mdast-util-gfm/lib/index.js", "(app-pages-browser)/./node_modules/mdast-util-phrasing/lib/index.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/footer.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/blockquote.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/break.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/code.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/delete.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/emphasis.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/heading.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/html.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/image-reference.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/image.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/index.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/inline-code.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/link-reference.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/link.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/list-item.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/list.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/paragraph.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/root.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/strong.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/table-cell.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/table-row.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/table.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/text.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/handlers/thematic-break.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/index.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/revert.js", "(app-pages-browser)/./node_modules/mdast-util-to-hast/lib/state.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/blockquote.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/break.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/code.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/definition.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/emphasis.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/heading.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/html.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/image-reference.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/image.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/index.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/inline-code.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/link-reference.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/link.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/list-item.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/list.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/paragraph.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/root.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/strong.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/text.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/handle/thematic-break.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-ordered.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet-other.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-bullet.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-emphasis.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-fence.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-list-item-indent.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-quote.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-rule-repetition.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-rule.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/check-strong.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/encode-character-reference.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/encode-info.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/format-code-as-indented.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/format-heading-as-setext.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/format-link-as-autolink.js", "(app-pages-browser)/./node_modules/mdast-util-to-markdown/lib/util/pattern-in-scope.js", "(app-pages-browser)/./node_modules/mdast-util-to-string/lib/index.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/attention.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/autolink.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/blank-line.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/block-quote.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/character-escape.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/character-reference.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/code-fenced.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/code-indented.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/code-text.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/content.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/definition.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/heading-atx.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/html-flow.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/html-text.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/label-end.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/label-start-image.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/label-start-link.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/line-ending.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/list.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/setext-underline.js", "(app-pages-browser)/./node_modules/micromark-core-commonmark/dev/lib/thematic-break.js", "(app-pages-browser)/./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/html.js", "(app-pages-browser)/./node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js", "(app-pages-browser)/./node_modules/micromark-extension-gfm-footnote/dev/lib/html.js", "(app-pages-browser)/./node_modules/micromark-extension-gfm-footnote/dev/lib/syntax.js", "(app-pages-browser)/./node_modules/micromark-extension-gfm-strikethrough/dev/lib/html.js", "(app-pages-browser)/./node_modules/micromark-extension-gfm-strikethrough/dev/lib/syntax.js", "(app-pages-browser)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js", "(app-pages-browser)/./node_modules/micromark-extension-gfm-table/dev/lib/html.js", "(app-pages-browser)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js", "(app-pages-browser)/./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js", "(app-pages-browser)/./node_modules/micromark-extension-gfm-tagfilter/lib/index.js", "(app-pages-browser)/./node_modules/micromark-extension-gfm-task-list-item/dev/lib/html.js", "(app-pages-browser)/./node_modules/micromark-extension-gfm-task-list-item/dev/lib/syntax.js", "(app-pages-browser)/./node_modules/micromark-extension-gfm/index.js", "(app-pages-browser)/./node_modules/micromark-factory-destination/dev/index.js", "(app-pages-browser)/./node_modules/micromark-factory-label/dev/index.js", "(app-pages-browser)/./node_modules/micromark-factory-space/dev/index.js", "(app-pages-browser)/./node_modules/micromark-factory-title/dev/index.js", "(app-pages-browser)/./node_modules/micromark-factory-whitespace/dev/index.js", "(app-pages-browser)/./node_modules/micromark-util-character/dev/index.js", "(app-pages-browser)/./node_modules/micromark-util-chunked/dev/index.js", "(app-pages-browser)/./node_modules/micromark-util-classify-character/dev/index.js", "(app-pages-browser)/./node_modules/micromark-util-combine-extensions/index.js", "(app-pages-browser)/./node_modules/micromark-util-decode-numeric-character-reference/dev/index.js", "(app-pages-browser)/./node_modules/micromark-util-decode-string/dev/index.js", "(app-pages-browser)/./node_modules/micromark-util-encode/index.js", "(app-pages-browser)/./node_modules/micromark-util-html-tag-name/index.js", "(app-pages-browser)/./node_modules/micromark-util-normalize-identifier/dev/index.js", "(app-pages-browser)/./node_modules/micromark-util-resolve-all/index.js", "(app-pages-browser)/./node_modules/micromark-util-sanitize-uri/dev/index.js", "(app-pages-browser)/./node_modules/micromark-util-subtokenize/dev/index.js", "(app-pages-browser)/./node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js", "(app-pages-browser)/./node_modules/micromark-util-symbol/lib/codes.js", "(app-pages-browser)/./node_modules/micromark-util-symbol/lib/constants.js", "(app-pages-browser)/./node_modules/micromark-util-symbol/lib/types.js", "(app-pages-browser)/./node_modules/micromark-util-symbol/lib/values.js", "(app-pages-browser)/./node_modules/micromark/dev/lib/constructs.js", "(app-pages-browser)/./node_modules/micromark/dev/lib/create-tokenizer.js", "(app-pages-browser)/./node_modules/micromark/dev/lib/initialize/content.js", "(app-pages-browser)/./node_modules/micromark/dev/lib/initialize/document.js", "(app-pages-browser)/./node_modules/micromark/dev/lib/initialize/flow.js", "(app-pages-browser)/./node_modules/micromark/dev/lib/initialize/text.js", "(app-pages-browser)/./node_modules/micromark/dev/lib/parse.js", "(app-pages-browser)/./node_modules/micromark/dev/lib/postprocess.js", "(app-pages-browser)/./node_modules/micromark/dev/lib/preprocess.js", "(app-pages-browser)/./node_modules/ms/index.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=E%3A%5Caugment-test%5Cutil%5CBookRealm%5Cfrontend%5Csrc%5Capp%5Cdashboard%5Creader%5C%5BbookId%5D%5C%5BchapterId%5D%5Cpage.tsx&server=false!", "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-runtime.development.js", "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js", "(app-pages-browser)/./node_modules/property-information/index.js", "(app-pages-browser)/./node_modules/property-information/lib/aria.js", "(app-pages-browser)/./node_modules/property-information/lib/find.js", "(app-pages-browser)/./node_modules/property-information/lib/hast-to-react.js", "(app-pages-browser)/./node_modules/property-information/lib/html.js", "(app-pages-browser)/./node_modules/property-information/lib/normalize.js", "(app-pages-browser)/./node_modules/property-information/lib/svg.js", "(app-pages-browser)/./node_modules/property-information/lib/util/case-insensitive-transform.js", "(app-pages-browser)/./node_modules/property-information/lib/util/case-sensitive-transform.js", "(app-pages-browser)/./node_modules/property-information/lib/util/create.js", "(app-pages-browser)/./node_modules/property-information/lib/util/defined-info.js", "(app-pages-browser)/./node_modules/property-information/lib/util/info.js", "(app-pages-browser)/./node_modules/property-information/lib/util/merge.js", "(app-pages-browser)/./node_modules/property-information/lib/util/schema.js", "(app-pages-browser)/./node_modules/property-information/lib/util/types.js", "(app-pages-browser)/./node_modules/property-information/lib/xlink.js", "(app-pages-browser)/./node_modules/property-information/lib/xml.js", "(app-pages-browser)/./node_modules/property-information/lib/xmlns.js", "(app-pages-browser)/./node_modules/react-markdown/lib/index.js", "(app-pages-browser)/./node_modules/remark-gfm/lib/index.js", "(app-pages-browser)/./node_modules/remark-parse/lib/index.js", "(app-pages-browser)/./node_modules/remark-rehype/lib/index.js", "(app-pages-browser)/./node_modules/space-separated-tokens/index.js", "(app-pages-browser)/./node_modules/style-to-js/cjs/index.js", "(app-pages-browser)/./node_modules/style-to-js/cjs/utilities.js", "(app-pages-browser)/./node_modules/style-to-object/cjs/index.js", "(app-pages-browser)/./node_modules/trim-lines/index.js", "(app-pages-browser)/./node_modules/trough/lib/index.js", "(app-pages-browser)/./node_modules/unified/lib/callable-instance.js", "(app-pages-browser)/./node_modules/unified/lib/index.js", "(app-pages-browser)/./node_modules/unist-util-is/lib/index.js", "(app-pages-browser)/./node_modules/unist-util-position/lib/index.js", "(app-pages-browser)/./node_modules/unist-util-stringify-position/lib/index.js", "(app-pages-browser)/./node_modules/unist-util-visit-parents/lib/color.js", "(app-pages-browser)/./node_modules/unist-util-visit-parents/lib/index.js", "(app-pages-browser)/./node_modules/unist-util-visit/lib/index.js", "(app-pages-browser)/./node_modules/vfile-message/lib/index.js", "(app-pages-browser)/./node_modules/vfile/lib/index.js", "(app-pages-browser)/./node_modules/vfile/lib/minpath.browser.js", "(app-pages-browser)/./node_modules/vfile/lib/minproc.browser.js", "(app-pages-browser)/./node_modules/vfile/lib/minurl.browser.js", "(app-pages-browser)/./node_modules/vfile/lib/minurl.shared.js", "(app-pages-browser)/./src/app/dashboard/reader/[bookId]/[chapterId]/page.tsx", "(app-pages-browser)/./src/components/BookmarkButton.tsx", "(app-pages-browser)/./src/components/BookmarkList.tsx", "(app-pages-browser)/./src/components/ChapterSummary.tsx", "(app-pages-browser)/./src/components/ReaderSettingsPanel.tsx", "(app-pages-browser)/./src/components/Translation/ImmersiveTranslation.tsx", "(app-pages-browser)/./src/components/Translation/TranslationHistory.tsx", "(app-pages-browser)/./src/components/Translation/TranslationPopup.tsx", "(app-pages-browser)/./src/components/Translation/TranslationSettings.tsx", "(app-pages-browser)/./src/components/Translation/index.js", "(app-pages-browser)/./src/components/Translation/translation.css", "(app-pages-browser)/./src/hooks/useBookmarks.ts", "(app-pages-browser)/./src/hooks/useReaderSettings.ts", "(app-pages-browser)/./src/hooks/useReadingProgress.ts", "(app-pages-browser)/./src/hooks/useTextSelection.ts", "(app-pages-browser)/./src/lib/translationAPI.ts", "(app-pages-browser)/./src/styles/draggable-panel.css", "(app-pages-browser)/./src/styles/resizable-sidebar.css"]}