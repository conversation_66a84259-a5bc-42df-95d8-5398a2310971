"""
书籍应用 URL 配置
"""

from django.urls import path
from . import views

app_name = 'books'

urlpatterns = [
    # 书籍上传
    path('upload/', views.BookUploadView.as_view(), name='book-upload'),

    # 书籍列表
    path('', views.BookListView.as_view(), name='book-list'),

    # 书籍详情
    path('<uuid:pk>/', views.BookDetailView.as_view(), name='book-detail'),

    # 删除书籍
    path('<uuid:book_id>/delete/', views.delete_book, name='book-delete'),

    # 获取章节内容
    path('<uuid:book_id>/chapters/<uuid:chapter_id>/',
         views.get_chapter_content, name='chapter-content'),

    # 获取书籍统计
    path('<uuid:book_id>/stats/', views.get_book_stats, name='book-stats'),

    # 章节总结功能
    path('<uuid:book_id>/chapters/<uuid:chapter_id>/summary/', views.ChapterSummaryView.as_view(), name='chapter-summary'),

    # 阅读进度管理
    path('<uuid:book_id>/progress/', views.ReadingProgressView.as_view(), name='reading-progress'),
    path('<uuid:book_id>/progress/get/', views.get_reading_progress, name='get-reading-progress'),

    # 阅读器设置
    path('reader-settings/', views.ReaderSettingsView.as_view(), name='reader-settings'),

    # 书签管理
    path('<uuid:book_id>/bookmarks/', views.BookmarkViewSet.as_view(), name='book-bookmarks'),
    path('bookmarks/', views.BookmarkViewSet.as_view(), name='all-bookmarks'),
    path('bookmarks/<uuid:bookmark_id>/update/', views.update_bookmark, name='update-bookmark'),
    path('bookmarks/<uuid:bookmark_id>/delete/', views.delete_bookmark, name='delete-bookmark'),

    # 翻译功能
    path('translate/', views.TranslationAPIView.as_view(), name='translation-api'),
    path('translation/history/', views.TranslationHistoryAPIView.as_view(), name='translation-history'),
    path('translation/settings/', views.TranslationSettingsAPIView.as_view(), name='translation-settings'),

    # 翻译功能
    path('translate/', views.TranslationView.as_view(), name='translate'),

    # 调试功能
    path('debug/test-auth/', views.test_auth, name='test-auth'),
    path('debug/user-books/', views.debug_user_books, name='debug-user-books'),
]
