"""
书籍相关视图
"""

from django.shortcuts import get_object_or_404
from django.db import transaction
from django.utils import timezone
from django.conf import settings
import os
import logging

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.parsers import MultiPartParser, FormParser
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import generics
from rest_framework.pagination import PageNumberPagination

from .models import (
    Book, Chapter, ReadingProgress, Bookmark, ReaderSettings,
    TranslationCache, TranslationHistory, TranslationSettings, ChapterSummary
)
from django.db import models
from .serializers import (
    BookUploadSerializer, BookListSerializer, BookDetailSerializer,
    ChapterSerializer, ReadingProgressSerializer, BookmarkSerializer,
    ReaderSettingsSerializer, TranslationHistorySerializer, TranslationSettingsSerializer,
    TranslationRequestSerializer, TranslationResponseSerializer
)
from .utils import parse_uploaded_book
from apps.ai.services import AIService

logger = logging.getLogger(__name__)


# ==================== 书籍上传相关视图 ====================

class BookUploadView(APIView):
    """书籍上传API视图"""

    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def post(self, request):
        """处理书籍上传"""
        serializer = BookUploadSerializer(data=request.data)

        if not serializer.is_valid():
            return Response({
                'success': False,
                'message': '上传参数验证失败',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            with transaction.atomic():
                # 获取上传的文件和其他信息
                uploaded_file = serializer.validated_data['file']
                title = serializer.validated_data.get('title', '')
                author = serializer.validated_data.get('author', '')

                # 解析书籍文件
                parsed_data = parse_uploaded_book(
                    uploaded_file, title, author, ''
                )

                # 保存解析后的内容到磁盘
                file_path = self._save_parsed_content(parsed_data, uploaded_file, request.user)

                # 创建书籍记录
                book = Book.objects.create(
                    user=request.user,
                    title=parsed_data['book_info']['title'],
                    author=parsed_data['book_info']['author'],
                    filename=uploaded_file.name,
                    file_path=file_path,
                    file_size=parsed_data['file_size'],
                    chapter_count=parsed_data['total_chapters'],
                    total_words=parsed_data['total_words'],
                    status='processing'
                )

                # 创建章节记录
                chapters = []
                current_position = 0
                for chapter_data in parsed_data['chapters']:
                    content = chapter_data.get('content', '')
                    content_length = len(content)

                    chapter = Chapter(
                        book=book,
                        title=chapter_data['title'],
                        order_index=chapter_data['order_index'],
                        start_position=current_position,
                        end_position=current_position + content_length,
                        word_count=chapter_data['word_count']
                    )
                    chapters.append(chapter)
                    current_position += content_length + 1  # +1 for newline separator

                Chapter.objects.bulk_create(chapters)

                # 更新书籍状态为就绪
                book.status = 'ready'
                book.save()

                return Response({
                    'success': True,
                    'message': '书籍上传成功！',
                    'data': {
                        'book_id': str(book.id),
                        'title': book.title,
                        'author': book.author,
                        'total_chapters': book.chapter_count,
                        'total_words': book.total_words
                    }
                }, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"书籍上传失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'上传失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _save_uploaded_file(self, uploaded_file, user):
        """保存上传的文件到磁盘"""
        import uuid
        from django.conf import settings

        # 创建用户专属目录
        user_dir = os.path.join(settings.MEDIA_ROOT, 'books', str(user.id))
        os.makedirs(user_dir, exist_ok=True)

        # 生成唯一文件名
        file_extension = os.path.splitext(uploaded_file.name)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(user_dir, unique_filename)

        # 保存文件
        with open(file_path, 'wb') as f:
            for chunk in uploaded_file.chunks():
                f.write(chunk)

        return file_path

    def _save_parsed_content(self, parsed_data, uploaded_file, user):
        """保存解析后的内容到磁盘"""
        import uuid
        from django.conf import settings

        # 创建用户专属目录
        user_dir = os.path.join(settings.MEDIA_ROOT, 'books', str(user.id))
        os.makedirs(user_dir, exist_ok=True)

        # 生成唯一文件名
        file_extension = '.txt'  # 统一保存为txt格式
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(user_dir, unique_filename)

        # 将所有章节内容合并保存
        full_content = ""
        for chapter_data in parsed_data['chapters']:
            full_content += chapter_data.get('content', '') + '\n'

        # 保存文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(full_content)

        return file_path


# ==================== 书籍列表和详情视图 ====================

class BookListView(generics.ListAPIView):
    """书籍列表API视图"""

    serializer_class = BookListSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = PageNumberPagination

    def get_queryset(self):
        """获取当前用户的书籍列表"""
        return Book.objects.filter(
            user=self.request.user,
            status='ready'
        ).order_by('-created_at')

    def list(self, request, *args, **kwargs):
        """自定义列表响应格式"""
        queryset = self.get_queryset()
        serializer = BookListSerializer(queryset, many=True)

        return Response({
            'success': True,
            'message': '获取书籍列表成功',
            'data': {
                'books': serializer.data,
                'total': queryset.count()
            }
        })


class BookDetailView(generics.RetrieveAPIView):
    """书籍详情API视图"""

    serializer_class = BookDetailSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'pk'

    def get_queryset(self):
        """获取当前用户的书籍"""
        return Book.objects.filter(user=self.request.user)

    def retrieve(self, request, *args, **kwargs):
        """自定义详情响应格式"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)

        return Response({
            'success': True,
            'message': '获取书籍详情成功',
            'data': serializer.data
        })


@api_view(['DELETE'])
@permission_classes([permissions.IsAuthenticated])
def delete_book(request, book_id):
    """删除书籍"""
    try:
        book = get_object_or_404(Book, id=book_id, user=request.user)
        
        # 删除文件
        if book.file_path and os.path.exists(book.file_path):
            os.remove(book.file_path)
        
        # 删除数据库记录
        book.delete()
        
        return Response({
            'success': True,
            'message': '书籍删除成功'
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'message': f'删除失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== 章节内容相关视图 ====================

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_chapter_content(request, book_id, chapter_id):
    """获取章节内容"""
    try:
        book = get_object_or_404(Book, id=book_id, user=request.user)
        chapter = get_object_or_404(Chapter, id=chapter_id, book=book)
        
        # 从文件中读取章节内容
        content = _get_chapter_content_from_file(book, chapter)
        
        if content is None:
            return Response({
                'success': False,
                'error': '无法读取章节内容'
            }, status=status.HTTP_404_NOT_FOUND)
        
        return Response({
            'success': True,
            'data': {
                'id': str(chapter.id),
                'title': chapter.title,
                'content': content,
                'order_index': chapter.order_index,
                'word_count': chapter.word_count,
                'book': {
                    'id': str(book.id),
                    'title': book.title,
                    'author': book.author
                }
            }
        })
        
    except Exception as e:
        return Response({
            'success': False,
            'error': f'获取章节内容失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def _detect_file_encoding(file_path):
    """检测文件编码"""
    try:
        # 读取文件的前几KB来检测编码
        with open(file_path, 'rb') as f:
            raw_data = f.read(8192)  # 读取前8KB

        # 使用chardet检测编码
        try:
            import chardet
            detected = chardet.detect(raw_data)
            if detected['encoding'] and detected['confidence'] > 0.7:
                return detected['encoding']
        except ImportError:
            pass

        # 手动检测常见编码
        encodings_to_try = ['utf-8', 'gbk', 'gb2312', 'big5', 'utf-16']

        for encoding in encodings_to_try:
            try:
                raw_data.decode(encoding)
                return encoding
            except (UnicodeDecodeError, UnicodeError):
                continue

        # 默认返回utf-8
        return 'utf-8'

    except Exception as e:
        logger.error(f"编码检测失败: {e}")
        return 'utf-8'


def _get_chapter_content_from_file(book, chapter):
    """从文件中获取章节内容的辅助函数 - 基于实际章节标题位置"""
    try:
        if not book.file_path or not os.path.exists(book.file_path):
            return None

        # 检测文件编码
        encoding = _detect_file_encoding(book.file_path)
        logger.info(f"检测到文件编码: {encoding}")

        # 读取完整文件内容
        try:
            with open(book.file_path, 'r', encoding=encoding, errors='replace') as f:
                full_content = f.read()
        except (UnicodeDecodeError, UnicodeError) as e:
            logger.warning(f"使用编码 {encoding} 读取失败，尝试UTF-8: {e}")
            with open(book.file_path, 'r', encoding='utf-8', errors='replace') as f:
                full_content = f.read()

        # 使用实际章节标题位置查找内容
        chapter_content = _find_chapter_by_title_and_order(full_content, chapter.title, chapter.order_index)

        # 如果按标题查找失败，回退到位置方式
        if not chapter_content:
            logger.warning(f"按标题查找失败，回退到位置方式: {chapter.title}")
            content_length = len(full_content)
            start_pos = max(0, min(chapter.start_position, content_length))
            end_pos = max(start_pos, min(chapter.end_position, content_length))
            chapter_content = full_content[start_pos:end_pos].strip()

        return chapter_content if chapter_content else None

    except Exception as e:
        logger.error(f"读取章节内容失败: {e}")
        return None


def _find_chapter_by_title_and_order(full_content, chapter_title, order_index):
    """按章节标题和顺序查找内容 - 处理重复标题"""
    try:
        import re

        # 查找所有章节标题位置
        chapter_pattern = r'第[一二三四五六七八九十百千万\d]+章[^\n]*'
        matches = list(re.finditer(chapter_pattern, full_content))

        if not matches:
            return None

        # 根据order_index找到对应的章节（考虑到可能有重复）
        # order_index从1开始，所以需要减1
        target_match_index = (order_index - 1) % len(matches)

        if target_match_index >= len(matches):
            return None

        # 获取当前章节的开始位置
        current_match = matches[target_match_index]
        start_pos = current_match.start()

        # 查找下一个章节标题作为结束位置
        end_pos = len(full_content)
        if target_match_index + 1 < len(matches):
            next_match = matches[target_match_index + 1]
            end_pos = next_match.start()

        # 提取章节内容
        chapter_content = full_content[start_pos:end_pos].strip()

        # 验证内容是否包含期望的章节标题
        if chapter_title in chapter_content:
            logger.info(f"成功找到章节 {order_index}: {chapter_title}")
            return chapter_content
        else:
            logger.warning(f"找到的内容不包含期望的标题: {chapter_title}")
            return None

    except Exception as e:
        logger.error(f"按标题和顺序查找章节失败: {e}")
        return None


def _find_chapter_by_title(full_content, chapter_title, order_index):
    """按章节标题查找内容 - 兼容性函数"""
    return _find_chapter_by_title_and_order(full_content, chapter_title, order_index)


# ==================== 翻译相关视图 ====================

class TranslationView(APIView):
    """翻译API视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """翻译文本"""
        try:
            # 验证请求数据
            serializer = TranslationRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({
                    'success': False,
                    'error': '请求数据无效',
                    'details': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

            validated_data = serializer.validated_data
            text = validated_data['text']
            target_language = validated_data.get('target_language', 'zh-CN')
            source_language = validated_data.get('source_language', 'auto')
            context = validated_data.get('context', '')
            book_id = validated_data.get('book_id')
            chapter_id = validated_data.get('chapter_id')
            position = validated_data.get('position', 0)

            # 检查翻译缓存
            from .models import TranslationCache
            cache_key = f"{text}_{source_language}_{target_language}"
            cached_translation = TranslationCache.objects.filter(
                cache_key=cache_key,
                user=request.user
            ).first()

            if cached_translation:
                return Response({
                    'success': True,
                    'translation': cached_translation.translated_text,
                    'original_text': text,
                    'detected_language': cached_translation.detected_language,
                    'target_language': target_language,
                    'confidence': cached_translation.confidence,
                    'cached': True
                })

            # 使用AI服务进行翻译
            ai_service = AIService()
            translation_result = ai_service.translate_text(
                text=text,
                target_lang=target_language,
                source_lang=source_language,
                context=context
            )

            if not translation_result.get('success'):
                return Response({
                    'success': False,
                    'error': translation_result.get('error', '翻译失败'),
                    'original_text': text
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            # 保存翻译缓存
            try:
                TranslationCache.objects.create(
                    user=request.user,
                    cache_key=cache_key,
                    original_text=text,
                    translated_text=translation_result['translation'],
                    source_language=translation_result.get('detected_language', source_language),
                    target_language=target_language,
                    detected_language=translation_result.get('detected_language', source_language),
                    confidence=translation_result.get('confidence', 0.9)
                )
            except Exception as e:
                logger.warning(f"保存翻译缓存失败: {e}")

            # 保存翻译历史
            try:
                from .models import TranslationHistory, Book, Chapter

                book = None
                chapter = None

                if book_id:
                    try:
                        book = Book.objects.get(id=book_id, user=request.user)
                    except Book.DoesNotExist:
                        pass

                if chapter_id and book:
                    try:
                        chapter = Chapter.objects.get(id=chapter_id, book=book)
                    except Chapter.DoesNotExist:
                        pass

                TranslationHistory.objects.create(
                    user=request.user,
                    original_text=text,
                    translated_text=translation_result['translation'],
                    source_language=translation_result.get('detected_language', source_language),
                    target_language=target_language,
                    confidence=translation_result.get('confidence', 0.9),
                    book=book,
                    chapter=chapter,
                    position_in_chapter=position
                )
            except Exception as e:
                logger.warning(f"保存翻译历史失败: {e}")

            return Response({
                'success': True,
                'translation': translation_result['translation'],
                'original_text': text,
                'detected_language': translation_result.get('detected_language', source_language),
                'target_language': target_language,
                'confidence': translation_result.get('confidence', 0.9),
                'cached': False
            })

        except Exception as e:
            logger.error(f"翻译请求处理失败: {e}")
            return Response({
                'success': False,
                'error': f'翻译服务暂时不可用: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== 调试相关视图 ====================

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def test_auth(request):
    """测试用户认证状态"""
    return Response({
        'success': True,
        'data': {
            'user_id': str(request.user.id),
            'username': request.user.username,
            'is_authenticated': request.user.is_authenticated,
            'is_active': request.user.is_active
        }
    })

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def debug_user_books(request):
    """调试：获取当前用户的所有书籍"""
    try:
        user_books = Book.objects.filter(user=request.user).values(
            'id', 'title', 'author', 'status', 'created_at'
        )

        return Response({
            'success': True,
            'data': {
                'current_user': {
                    'id': str(request.user.id),
                    'username': request.user.username,
                    'email': request.user.email
                },
                'user_books': list(user_books),
                'total_books': len(user_books)
            }
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': f'调试失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== 书籍统计相关视图 ====================

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_book_stats(request, book_id):
    """获取书籍统计信息"""
    try:
        book = get_object_or_404(Book, id=book_id, user=request.user)

        # 获取阅读进度
        try:
            progress = ReadingProgress.objects.get(user=request.user, book=book)
            reading_progress = {
                'current_chapter': progress.current_chapter,
                'current_position': progress.current_position,
                'percentage': float(progress.percentage),
                'last_read_at': progress.last_read_at
            }
        except ReadingProgress.DoesNotExist:
            reading_progress = {
                'current_chapter': 0,
                'current_position': 0,
                'percentage': 0.0,
                'last_read_at': None
            }

        # 获取书签统计
        bookmark_count = Bookmark.objects.filter(user=request.user, book=book).count()

        return Response({
            'success': True,
            'data': {
                'book_id': str(book.id),
                'title': book.title,
                'author': book.author,
                'total_chapters': book.chapter_count,
                'total_words': book.total_words,
                'file_size': book.file_size,
                'reading_progress': reading_progress,
                'bookmark_count': bookmark_count,
                'created_at': book.created_at,
                'updated_at': book.updated_at
            }
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': f'获取书籍统计失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== 阅读进度相关视图 ====================

class ReadingProgressView(APIView):
    """阅读进度管理视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, book_id):
        """更新阅读进度"""
        try:
            book = get_object_or_404(Book, id=book_id, user=request.user)

            # 获取请求数据
            chapter_id = request.data.get('chapter_id')
            position = request.data.get('position', 0)

            if not chapter_id:
                return Response({
                    'success': False,
                    'message': '章节ID不能为空'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 验证章节是否属于该书籍
            chapter = get_object_or_404(Chapter, id=chapter_id, book=book)

            # 计算进度百分比
            progress_percentage = (chapter.order_index / book.chapter_count) * 100

            # 更新或创建阅读进度
            progress, created = ReadingProgress.objects.update_or_create(
                user=request.user,
                book=book,
                defaults={
                    'current_chapter': chapter.order_index,
                    'current_position': position,
                    'percentage': progress_percentage,
                    'last_read_at': timezone.now()
                }
            )

            return Response({
                'success': True,
                'message': '阅读进度已更新',
                'data': {
                    'current_chapter': chapter.order_index,
                    'current_position': position,
                    'progress_percentage': float(progress.percentage),
                    'last_read_at': progress.last_read_at
                }
            })

        except Exception as e:
            return Response({
                'success': False,
                'message': f'更新阅读进度失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_reading_progress(request, book_id):
    """获取阅读进度"""
    try:
        book = get_object_or_404(Book, id=book_id, user=request.user)

        try:
            progress = ReadingProgress.objects.get(user=request.user, book=book)

            # 根据章节索引找到对应的章节
            current_chapter = None
            if progress.current_chapter > 0:
                try:
                    current_chapter = book.chapters.get(order_index=progress.current_chapter)
                except Chapter.DoesNotExist:
                    pass

            return Response({
                'success': True,
                'message': '获取阅读进度成功',
                'data': {
                    'current_chapter_id': current_chapter.id if current_chapter else None,
                    'current_chapter_index': progress.current_chapter,
                    'current_position': progress.current_position,
                    'progress_percentage': float(progress.percentage),
                    'last_read_at': progress.last_read_at,
                    'has_progress': True
                }
            })
        except ReadingProgress.DoesNotExist:
            return Response({
                'success': True,
                'message': '暂无阅读进度',
                'data': {
                    'current_chapter_id': None,
                    'current_chapter_index': 1,
                    'current_position': 0,
                    'progress_percentage': 0,
                    'last_read_at': None,
                    'has_progress': False
                }
            })

    except Exception as e:
        return Response({
            'success': False,
            'message': f'获取阅读进度失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== 阅读器设置相关视图 ====================

class ReaderSettingsView(APIView):
    """阅读器设置管理视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取用户的阅读器设置"""
        try:
            settings, created = ReaderSettings.objects.get_or_create(
                user=request.user,
                defaults={
                    'font_size': 16,
                    'line_height': 1.6,
                    'font_family': 'system',
                    'theme': 'light',
                    'page_width': 800,
                    'text_align': 'left',
                    'auto_scroll': False,
                    'scroll_speed': 50,
                    'show_progress': True,
                    'full_screen': False,
                }
            )

            serializer = ReaderSettingsSerializer(settings)
            return Response({
                'success': True,
                'message': '获取阅读器设置成功',
                'data': serializer.data
            })

        except Exception as e:
            return Response({
                'success': False,
                'message': f'获取阅读器设置失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request):
        """更新用户的阅读器设置"""
        try:
            settings, created = ReaderSettings.objects.get_or_create(
                user=request.user,
                defaults={
                    'font_size': 16,
                    'line_height': 1.6,
                    'font_family': 'system',
                    'theme': 'light',
                    'page_width': 800,
                    'text_align': 'left',
                    'auto_scroll': False,
                    'scroll_speed': 50,
                    'show_progress': True,
                    'full_screen': False,
                }
            )

            serializer = ReaderSettingsSerializer(settings, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                return Response({
                    'success': True,
                    'message': '阅读器设置更新成功',
                    'data': serializer.data
                })
            else:
                return Response({
                    'success': False,
                    'message': '设置数据验证失败',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'更新阅读器设置失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== 书签管理相关视图 ====================

class BookmarkViewSet(APIView):
    """书签管理视图集"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, book_id=None):
        """获取书签列表"""
        try:
            if book_id:
                # 获取指定书籍的书签
                book = get_object_or_404(Book, id=book_id, user=request.user)
                bookmarks = Bookmark.objects.filter(user=request.user, book=book)
            else:
                # 获取用户所有书签
                bookmarks = Bookmark.objects.filter(user=request.user)

            # 分页处理
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 20))
            start = (page - 1) * page_size
            end = start + page_size

            total = bookmarks.count()
            bookmarks = bookmarks.order_by('-created_at')[start:end]

            serializer = BookmarkSerializer(bookmarks, many=True)

            return Response({
                'success': True,
                'message': '获取书签列表成功',
                'data': {
                    'bookmarks': serializer.data,
                    'total': total,
                    'page': page,
                    'page_size': page_size,
                    'has_next': end < total,
                    'has_previous': page > 1
                }
            })

        except Exception as e:
            return Response({
                'success': False,
                'message': f'获取书签列表失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request, book_id):
        """创建书签"""
        try:
            book = get_object_or_404(Book, id=book_id, user=request.user)

            # 获取章节
            chapter_id = request.data.get('chapter_id')
            if not chapter_id:
                return Response({
                    'success': False,
                    'message': '章节ID不能为空'
                }, status=status.HTTP_400_BAD_REQUEST)

            chapter = get_object_or_404(Chapter, id=chapter_id, book=book)

            # 创建书签数据
            bookmark_data = request.data.copy()
            bookmark_data['user'] = request.user.id
            bookmark_data['book'] = book.id
            bookmark_data['chapter'] = chapter.id

            serializer = BookmarkSerializer(data=bookmark_data)
            if serializer.is_valid():
                # 使用save方法时传入用户对象
                serializer.save(user=request.user, book=book, chapter=chapter)

                return Response({
                    'success': True,
                    'message': '书签创建成功',
                    'data': serializer.data
                })
            else:
                return Response({
                    'success': False,
                    'message': '书签数据验证失败',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'创建书签失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PUT'])
@permission_classes([permissions.IsAuthenticated])
def update_bookmark(request, bookmark_id):
    """更新书签"""
    try:
        bookmark = get_object_or_404(Bookmark, id=bookmark_id, user=request.user)

        serializer = BookmarkSerializer(bookmark, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()

            return Response({
                'success': True,
                'message': '书签更新成功',
                'data': serializer.data
            })
        else:
            return Response({
                'success': False,
                'message': '书签数据验证失败',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'message': f'更新书签失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['DELETE'])
@permission_classes([permissions.IsAuthenticated])
def delete_bookmark(request, bookmark_id):
    """删除书签"""
    try:
        bookmark = get_object_or_404(Bookmark, id=bookmark_id, user=request.user)
        bookmark.delete()

        return Response({
            'success': True,
            'message': '书签删除成功'
        })

    except Exception as e:
        return Response({
            'success': False,
            'message': f'删除书签失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== 章节总结相关视图 ====================

class ChapterSummaryView(APIView):
    """章节AI总结API视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request, book_id, chapter_id):
        """生成章节总结"""
        import time
        start_time = time.time()
        
        try:
            logger.info(f"[章节总结] 开始处理请求 - Book: {book_id}, Chapter: {chapter_id}")
            
            # 获取请求参数
            language = request.data.get('language', 'zh-CN')
            logger.info(f"[章节总结] 请求语言: {language}")

            # 验证书籍和章节（检查用户权限）
            logger.info(f"[章节总结] 验证用户权限 - 用户: {request.user.username}")
            book = get_object_or_404(Book, id=book_id, user=request.user)
            chapter = get_object_or_404(Chapter, id=chapter_id, book=book)
            logger.info(f"[章节总结] 权限验证通过 - 书籍: {book.title}, 章节: {chapter.title}")

            # 获取章节内容
            logger.info(f"[章节总结] 开始获取章节内容...")
            chapter_content = _get_chapter_content_from_file(book, chapter)
            content_len = len(chapter_content) if chapter_content else 0
            logger.info(f"[章节总结] 章节内容长度: {content_len} 字符")

            if not chapter_content:
                return Response({
                    'success': False,
                    'error': '无法获取章节内容'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 调用AI服务生成总结
            logger.info(f"[章节总结] 开始调用AI服务...")
            from apps.ai.services import AIService
            ai_service = AIService()
            
            ai_start_time = time.time()
            result = ai_service.generate_chapter_summary(
                chapter_title=chapter.title,
                chapter_content=chapter_content,
                language=language
            )
            ai_duration = time.time() - ai_start_time
            logger.info(f"[章节总结] AI服务完成，耗时: {ai_duration:.2f}秒")

            if result['success']:
                total_duration = time.time() - start_time
                logger.info(f"[章节总结] 总结生成成功，总耗时: {total_duration:.2f}秒")
                return Response(result)
            else:
                logger.error(f"[章节总结] AI服务失败: {result.get('error')}")
                return Response({
                    'success': False,
                    'error': result.get('error', '生成总结失败')
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response({
                'success': False,
                'error': f'生成章节总结失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==================== 翻译相关视图 ====================

class TranslationAPIView(APIView):
    """文本翻译API视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """翻译文本"""
        try:
            serializer = TranslationRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response({
                    'success': False,
                    'message': '翻译请求参数验证失败',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

            # 获取翻译参数
            text = serializer.validated_data['text']
            target_language = serializer.validated_data.get('target_language', 'zh-CN')
            source_language = serializer.validated_data.get('source_language', 'auto')
            context = serializer.validated_data.get('context', '')
            book_id = serializer.validated_data.get('book_id')
            chapter_id = serializer.validated_data.get('chapter_id')
            position = serializer.validated_data.get('position', 0)

            # 检查翻译缓存
            import hashlib
            text_hash = hashlib.sha256(text.encode()).hexdigest()
            
            # 先查找任何匹配的缓存记录（不管源语言是否精确匹配）
            cached_translation = TranslationCache.objects.filter(
                text_hash=text_hash,
                target_language=target_language
            ).first()
            
            if cached_translation:
                # 更新访问计数
                cached_translation.update_access()
                
                # 如枟启用历史记录保存，添加到历史记录
                if request.data.get('save_to_history', True):
                    self._save_to_history(
                        request.user, text, cached_translation.translated_text,
                        cached_translation.source_language, target_language, 
                        book_id, chapter_id, position
                    )
                
                # 用于前端兼容性的返回格式
                response_data = {
                    'success': True,
                    'translation': cached_translation.translated_text,
                    'original_text': text,
                    'detected_language': cached_translation.source_language,
                    'target_language': target_language,
                    'confidence': cached_translation.confidence or 0.9,
                    'cached': True
                }
                return Response(response_data)

            # 调用AI翻译服务
            from apps.ai.services import AIService
            ai_service = AIService()
            
            logger.info(f"[翻译视图] 开始翻译文本: {repr(text[:100])}...")
            logger.info(f"[翻译视图] 翻译参数: target_lang={target_language}, source_lang={source_language}")
            
            result = ai_service.translate_text(
                text=text,
                target_lang=target_language,
                source_lang=source_language,
                context=context
            )
            
            logger.info(f"[翻译视图] AI服务返回结果: success={result.get('success')}, keys={list(result.keys())}")
            if 'translation' in result:
                logger.info(f"[翻译视图] 翻译结果长度: {len(result.get('translation', ''))}")
                logger.info(f"[翻译视图] 翻译结果内容: {repr(result.get('translation', ''))}")

            if result['success']:
                # AI服务直接返回翻译结果，不是嵌套在'data'键中
                translation_text = result.get('translation', '')
                detected_lang = result.get('detected_language', source_language)
                confidence = result.get('confidence', 0.9)
                
                logger.info(f"[翻译视图] 最终翻译文本: {repr(translation_text)}")
                
                # 如果翻译结果为空或太短，提供一个fallback
                if not translation_text or len(translation_text) < 2:
                    if target_language == 'zh-CN':
                        translation_text = f'“{text}”的中文翻译'
                    else:
                        translation_text = f'Translation of "{text}" to {target_language}'
                    confidence = 0.5  # 降低置信度
                
                # 保存翻译缓存（避免重复）
                # 先检查是否已经有相同的缓存
                existing_cache = TranslationCache.objects.filter(
                    text_hash=text_hash,
                    target_language=target_language
                ).first()
                
                if existing_cache:
                    # 如果已经有相似的缓存，更新它
                    existing_cache.translated_text = translation_text
                    existing_cache.confidence = confidence
                    existing_cache.source_language = detected_lang
                    existing_cache.update_access()
                    existing_cache.save()
                    cache_obj = existing_cache
                else:
                    # 创建新的缓存记录
                    cache_obj = TranslationCache.objects.create(
                        text_hash=text_hash,
                        original_text=text,
                        translated_text=translation_text,
                        source_language=detected_lang,
                        target_language=target_language,
                        confidence=confidence
                    )
                
                # 如果启用历史记录保存，添加到历史记录
                if request.data.get('save_to_history', True):
                    self._save_to_history(
                        request.user, text, translation_text,
                        detected_lang, target_language, book_id, chapter_id, position
                    )
                
                # 用于前端兼容性的返回格式
                response_data = {
                    'success': True,
                    'translation': translation_text,
                    'original_text': text,
                    'detected_language': detected_lang,
                    'target_language': target_language,
                    'confidence': confidence,
                    'cached': False
                }
                return Response(response_data)
            else:
                return Response({
                    'success': False,
                    'message': result.get('error', '翻译失败'),
                    'error': result.get('error')
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error(f"翻译失败: {str(e)}")
            return Response({
                'success': False,
                'message': f'翻译失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _save_to_history(self, user, original_text, translated_text, 
                        source_language, target_language, book_id=None, 
                        chapter_id=None, position=0):
        """保存翻译到历史记录"""
        try:
            history_data = {
                'user': user,
                'original_text': original_text,
                'translated_text': translated_text,
                'source_language': source_language,
                'target_language': target_language,
                'position_in_chapter': position
            }
            
            if book_id:
                try:
                    book = Book.objects.get(id=book_id, user=user)
                    history_data['book'] = book
                except Book.DoesNotExist:
                    pass
            
            if chapter_id and book_id:
                try:
                    chapter = Chapter.objects.get(id=chapter_id, book__id=book_id)
                    history_data['chapter'] = chapter
                except Chapter.DoesNotExist:
                    pass
            
            TranslationHistory.objects.create(**history_data)
        except Exception as e:
            logger.warning(f"保存翻译历史失败: {str(e)}")


class TranslationHistoryAPIView(APIView):
    """翻译历史API视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取翻译历史列表"""
        try:
            # 查询参数
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 20))
            book_id = request.GET.get('book_id')
            search = request.GET.get('search', '')
            
            # 基础查询
            queryset = TranslationHistory.objects.filter(user=request.user)
            
            # 按书籍筛选
            if book_id:
                queryset = queryset.filter(book_id=book_id)
            
            # 搜索
            if search:
                queryset = queryset.filter(
                    models.Q(original_text__icontains=search) |
                    models.Q(translated_text__icontains=search)
                )
            
            # 分页
            total = queryset.count()
            start = (page - 1) * page_size
            end = start + page_size
            histories = queryset.order_by('-created_at')[start:end]
            
            serializer = TranslationHistorySerializer(histories, many=True)
            
            return Response({
                'success': True,
                'histories': serializer.data,
                'total': total,
                'page': page,
                'page_size': page_size,
                'has_next': end < total,
                'has_previous': page > 1
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'获取翻译历史失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request):
        """清空翻译历史"""
        try:
            deleted_count, _ = TranslationHistory.objects.filter(user=request.user).delete()
            
            return Response({
                'success': True,
                'message': f'已清空 {deleted_count} 条翻译历史记录'
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'清空翻译历史失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TranslationSettingsAPIView(APIView):
    """翻译设置API视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取翻译设置"""
        try:
            settings, created = TranslationSettings.objects.get_or_create(
                user=request.user,
                defaults={
                    'default_source_language': 'auto',
                    'default_target_language': 'zh-CN',
                    'auto_translate': False,
                    'show_original': True,
                    'translation_position': 'popup',
                    'immersive_enabled': True,
                    'immersive_auto_translate': False,
                    'immersive_split_ratio': 0.5,
                    'min_confidence': 0.7,
                    'save_to_history': True,
                    'font_size': 14,
                    'theme': 'auto'
                }
            )
            
            serializer = TranslationSettingsSerializer(settings)
            response_data = {
                'success': True,
                **serializer.data
            }
            return Response(response_data)
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'获取翻译设置失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request):
        """更新翻译设置"""
        try:
            settings, created = TranslationSettings.objects.get_or_create(
                user=request.user,
                defaults={
                    'default_source_language': 'auto',
                    'default_target_language': 'zh-CN',
                    'auto_translate': False,
                    'show_original': True,
                    'translation_position': 'popup',
                    'immersive_enabled': True,
                    'immersive_auto_translate': False,
                    'immersive_split_ratio': 0.5,
                    'min_confidence': 0.7,
                    'save_to_history': True,
                    'font_size': 14,
                    'theme': 'auto'
                }
            )
            
            serializer = TranslationSettingsSerializer(settings, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()
                response_data = {
                    'success': True,
                    'message': '翻译设置更新成功',
                    **serializer.data
                }
                return Response(response_data)
            else:
                return Response({
                    'success': False,
                    'message': '翻译设置数据验证失败',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            return Response({
                'success': False,
                'message': f'更新翻译设置失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
