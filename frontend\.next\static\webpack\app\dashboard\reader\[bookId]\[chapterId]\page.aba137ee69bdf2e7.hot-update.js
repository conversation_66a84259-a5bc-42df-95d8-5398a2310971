"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/reader/[bookId]/[chapterId]/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/reader/[bookId]/[chapterId]/page.tsx":
/*!****************************************************************!*\
  !*** ./src/app/dashboard/reader/[bookId]/[chapterId]/page.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ReaderPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_bookApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/bookApi */ \"(app-pages-browser)/./src/lib/bookApi.ts\");\n/* harmony import */ var _hooks_useReadingProgress__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useReadingProgress */ \"(app-pages-browser)/./src/hooks/useReadingProgress.ts\");\n/* harmony import */ var _hooks_useReaderSettings__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useReaderSettings */ \"(app-pages-browser)/./src/hooks/useReaderSettings.ts\");\n/* harmony import */ var _hooks_useBookmarks__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useBookmarks */ \"(app-pages-browser)/./src/hooks/useBookmarks.ts\");\n/* harmony import */ var _components_ReaderSettingsPanel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ReaderSettingsPanel */ \"(app-pages-browser)/./src/components/ReaderSettingsPanel.tsx\");\n/* harmony import */ var _components_BookmarkButton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/BookmarkButton */ \"(app-pages-browser)/./src/components/BookmarkButton.tsx\");\n/* harmony import */ var _components_BookmarkList__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/BookmarkList */ \"(app-pages-browser)/./src/components/BookmarkList.tsx\");\n/* harmony import */ var _components_Translation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/Translation */ \"(app-pages-browser)/./src/components/Translation/index.js\");\n/* harmony import */ var _components_ChapterSummary__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ChapterSummary */ \"(app-pages-browser)/./src/components/ChapterSummary.tsx\");\n/* harmony import */ var _hooks_useTextSelection__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/hooks/useTextSelection */ \"(app-pages-browser)/./src/hooks/useTextSelection.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mouse-pointer-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/languages.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coffee.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bookmark.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/maximize-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BookOpen,Bookmark,ChevronLeft,ChevronRight,Clock,Coffee,Eye,EyeOff,History,Home,Languages,List,Maximize2,Minimize2,MousePointer2,Pause,Play,RotateCcw,Save,Search,Settings,Sidebar,Type,X,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _styles_resizable_sidebar_css__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/styles/resizable-sidebar.css */ \"(app-pages-browser)/./src/styles/resizable-sidebar.css\");\n/* harmony import */ var _styles_draggable_panel_css__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/styles/draggable-panel.css */ \"(app-pages-browser)/./src/styles/draggable-panel.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ReaderPage() {\n    var _chapter_book, _contentRef_current, _chapter_word_count, _chapter_word_count1;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const bookId = params.bookId;\n    const chapterId = params.chapterId;\n    const [chapter, setChapter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [allChapters, setAllChapters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentChapterIndex, setCurrentChapterIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showBookmarks, setShowBookmarks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTranslationHistory, setShowTranslationHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTranslationSettings, setShowTranslationSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showImmersiveTranslation, setShowImmersiveTranslation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [translationSettings, setTranslationSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showChapterSummary, setShowChapterSummary] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [forceUpdate, setForceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // 新增UI控制状态\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showToolbar, setShowToolbar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showChapterList, setShowChapterList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [autoHideToolbar, setAutoHideToolbar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [toolbarTimeout, setToolbarTimeout] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 增强的阅读体验状态\n    const [readingMode, setReadingMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"normal\");\n    const [showReadingStats, setShowReadingStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [autoScroll, setAutoScroll] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [scrollSpeed, setScrollSpeed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(50);\n    const [showQuickActions, setShowQuickActions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [wordHighlight, setWordHighlight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lineHighlight, setLineHighlight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showKeyboardHelp, setShowKeyboardHelp] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarWidth, setSidebarWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(320) // 默认320px\n    ;\n    const [isResizing, setIsResizing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 拖动相关状态\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOffset, setDragOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [panelPosition, setPanelPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 20,\n        y: 100\n    }) // 默认位置\n    ;\n    const contentRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const sidebarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const resizerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 使用文本选择Hook进行划词翻译\n    const { showTranslation, selectedText, selectionPosition, hideTranslation, clearSelection, getTextPosition } = (0,_hooks_useTextSelection__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(contentRef, {\n        minLength: 2,\n        maxLength: 1000,\n        enableTranslation: true\n    });\n    // 使用阅读进度管理 Hook\n    const { progress, saving, totalReadingTime, updateProgress, startReading, stopReading, getFormattedReadingTime } = (0,_hooks_useReadingProgress__WEBPACK_IMPORTED_MODULE_5__.useReadingProgress)({\n        bookId,\n        chapterId,\n        autoSave: true,\n        saveInterval: 3000 // 3秒自动保存\n    });\n    // 使用阅读器设置 Hook\n    const { settings, tempSettings, getThemeStyles, updateTempSetting, saveTempSettings } = (0,_hooks_useReaderSettings__WEBPACK_IMPORTED_MODULE_6__.useReaderSettings)();\n    // 使用当前有效的设置（临时设置优先）\n    const currentSettings = tempSettings || settings;\n    // 使用书签 Hook\n    const { findBookmarkByPosition } = (0,_hooks_useBookmarks__WEBPACK_IMPORTED_MODULE_7__.useBookmarks)({\n        bookId\n    });\n    // 加载翻译设置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadTranslationSettings = async ()=>{\n            try {\n                const { translationAPI } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/translationAPI */ \"(app-pages-browser)/./src/lib/translationAPI.ts\"));\n                const response = await translationAPI.getSettings();\n                console.log(\"翻译设置加载响应:\", response);\n                if (response.success || response.default_target_language) {\n                    // 处理两种可能的响应格式\n                    const settings = response.data || response;\n                    setTranslationSettings(settings);\n                    console.log(\"翻译设置已加载:\", settings);\n                } else {\n                    // 设置默认值\n                    const defaultSettings = {\n                        default_source_language: \"auto\",\n                        default_target_language: \"zh-CN\",\n                        immersive_enabled: true,\n                        immersive_auto_translate: false,\n                        immersive_split_ratio: 0.5\n                    };\n                    setTranslationSettings(defaultSettings);\n                    console.log(\"使用默认翻译设置:\", defaultSettings);\n                }\n            } catch (error) {\n                console.error(\"Failed to load translation settings:\", error);\n                // 设置默认值\n                const defaultSettings = {\n                    default_source_language: \"auto\",\n                    default_target_language: \"zh-CN\",\n                    immersive_enabled: true,\n                    immersive_auto_translate: false,\n                    immersive_split_ratio: 0.5\n                };\n                setTranslationSettings(defaultSettings);\n                console.log(\"加载失败，使用默认翻译设置:\", defaultSettings);\n            }\n        };\n        loadTranslationSettings();\n    }, []);\n    // 切换沉浸式翻译\n    const toggleImmersiveTranslation = ()=>{\n        var _chapter_content;\n        console.log(\"切换沉浸式翻译:\", !showImmersiveTranslation);\n        console.log(\"当前翻译设置:\", translationSettings);\n        console.log(\"当前章节内容:\", (chapter === null || chapter === void 0 ? void 0 : (_chapter_content = chapter.content) === null || _chapter_content === void 0 ? void 0 : _chapter_content.substring(0, 100)) + \"...\");\n        setShowImmersiveTranslation(!showImmersiveTranslation);\n    };\n    // 侧边栏拖拽调整大小功能\n    const handleMouseDown = (e)=>{\n        e.preventDefault();\n        setIsResizing(true);\n        document.addEventListener(\"mousemove\", handleSidebarMouseMove);\n        document.addEventListener(\"mouseup\", handleMouseUp);\n        document.body.classList.add(\"resizing\");\n    };\n    const handleSidebarMouseMove = (e)=>{\n        if (!isResizing) return;\n        const newWidth = Math.max(240, Math.min(600, e.clientX));\n        setSidebarWidth(newWidth);\n    };\n    const handleMouseUp = ()=>{\n        setIsResizing(false);\n        document.removeEventListener(\"mousemove\", handleSidebarMouseMove);\n        document.removeEventListener(\"mouseup\", handleMouseUp);\n        document.body.classList.remove(\"resizing\");\n    };\n    // 双击重置侧边栏宽度\n    const handleDoubleClick = ()=>{\n        setSidebarWidth(320) // 重置为默认宽度\n        ;\n    };\n    // 拖动面板相关函数\n    const handlePanelMouseDown = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n        const rect = e.currentTarget.parentElement.getBoundingClientRect();\n        setDragOffset({\n            x: e.clientX - rect.left,\n            y: e.clientY - rect.top\n        });\n        document.addEventListener(\"mousemove\", handlePanelMouseMove);\n        document.addEventListener(\"mouseup\", handlePanelMouseUp);\n        document.body.classList.add(\"dragging\");\n    };\n    const handlePanelMouseMove = (e)=>{\n        if (!isDragging) return;\n        const newX = Math.max(0, Math.min(window.innerWidth - 320, e.clientX - dragOffset.x));\n        const newY = Math.max(0, Math.min(window.innerHeight - 400, e.clientY - dragOffset.y));\n        setPanelPosition({\n            x: newX,\n            y: newY\n        });\n    };\n    const handlePanelMouseUp = ()=>{\n        setIsDragging(false);\n        document.removeEventListener(\"mousemove\", handlePanelMouseMove);\n        document.removeEventListener(\"mouseup\", handlePanelMouseUp);\n        document.body.classList.remove(\"dragging\");\n    };\n    // 获取书籍详情和章节列表\n    const fetchBookAndChapter = async ()=>{\n        try {\n            setLoading(true);\n            // 获取书籍详情（包含章节列表）\n            const bookResponse = await _lib_bookApi__WEBPACK_IMPORTED_MODULE_4__.bookAPI.getBookDetail(bookId);\n            if (bookResponse.success && bookResponse.data.chapters) {\n                setAllChapters(bookResponse.data.chapters);\n                // 找到当前章节的索引\n                const chapterIndex = bookResponse.data.chapters.findIndex((ch)=>ch.id === chapterId);\n                setCurrentChapterIndex(chapterIndex >= 0 ? chapterIndex : 0);\n            }\n            // 获取当前章节内容\n            const chapterResponse = await _lib_bookApi__WEBPACK_IMPORTED_MODULE_4__.bookAPI.getChapterContent(bookId, chapterId);\n            if (chapterResponse.success) {\n                setChapter(chapterResponse.data);\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch chapter content:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_15__[\"default\"].error(\"获取章节内容失败\");\n            router.push(\"/dashboard/books/\".concat(bookId));\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 切换到指定章节\n    const navigateToChapter = (targetChapterIndex)=>{\n        if (targetChapterIndex >= 0 && targetChapterIndex < allChapters.length) {\n            const targetChapter = allChapters[targetChapterIndex];\n            router.push(\"/dashboard/reader/\".concat(bookId, \"/\").concat(targetChapter.id));\n        }\n    };\n    // 上一章\n    const goToPreviousChapter = ()=>{\n        if (currentChapterIndex > 0) {\n            navigateToChapter(currentChapterIndex - 1);\n        }\n    };\n    // 下一章\n    const goToNextChapter = ()=>{\n        if (currentChapterIndex < allChapters.length - 1) {\n            navigateToChapter(currentChapterIndex + 1);\n        }\n    };\n    // 处理书签跳转\n    const handleBookmarkJump = async (bookmark)=>{\n        // 如果书签在当前章节，直接滚动到位置\n        if (bookmark.chapter === chapterId) {\n            if (bookmark.scroll_position > 0) {\n                const contentElement = contentRef.current;\n                if (contentElement) {\n                    contentElement.scrollTop = bookmark.scroll_position;\n                }\n            }\n            setShowBookmarks(false);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_15__[\"default\"].success(\"已跳转到书签位置\");\n        } else {\n            // 如果书签在其他章节，跳转到对应章节\n            router.push(\"/dashboard/reader/\".concat(bookId, \"/\").concat(bookmark.chapter));\n            setShowBookmarks(false);\n        }\n    };\n    // 切换全屏模式\n    const toggleFullscreen = ()=>{\n        if (!document.fullscreenElement) {\n            document.documentElement.requestFullscreen();\n            setIsFullscreen(true);\n        } else {\n            document.exitFullscreen();\n            setIsFullscreen(false);\n        }\n    };\n    // 工具栏自动隐藏\n    const handleMouseMove = ()=>{\n        if (autoHideToolbar) {\n            setShowToolbar(true);\n            if (toolbarTimeout) {\n                clearTimeout(toolbarTimeout);\n            }\n            const timeout = setTimeout(()=>{\n                setShowToolbar(false);\n            }, 3000);\n            setToolbarTimeout(timeout);\n        }\n    };\n    // 键盘快捷键 - 增强版\n    const handleKeyPress = (e)=>{\n        // 如果正在输入框中，不处理快捷键\n        if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {\n            return;\n        }\n        // Ctrl/Cmd + 键的组合\n        if (e.ctrlKey || e.metaKey) {\n            switch(e.key){\n                case \"=\":\n                case \"+\":\n                    e.preventDefault();\n                    // 增大字体\n                    const currentSizeUp = (currentSettings === null || currentSettings === void 0 ? void 0 : currentSettings.font_size) || 18;\n                    const newSizeUp = Math.min(36, currentSizeUp + 2);\n                    if (newSizeUp !== currentSizeUp) {\n                        updateTempSetting(\"font_size\", newSizeUp);\n                        setTimeout(()=>saveTempSettings({\n                                silent: true\n                            }), 100);\n                    }\n                    break;\n                case \"-\":\n                    e.preventDefault();\n                    // 减小字体\n                    const currentSizeDown = (currentSettings === null || currentSettings === void 0 ? void 0 : currentSettings.font_size) || 18;\n                    const newSizeDown = Math.max(12, currentSizeDown - 2);\n                    if (newSizeDown !== currentSizeDown) {\n                        updateTempSetting(\"font_size\", newSizeDown);\n                        setTimeout(()=>saveTempSettings({\n                                silent: true\n                            }), 100);\n                    }\n                    break;\n                case \"0\":\n                    e.preventDefault();\n                    // 重置字体大小\n                    const currentSizeReset = (currentSettings === null || currentSettings === void 0 ? void 0 : currentSettings.font_size) || 18;\n                    if (currentSizeReset !== 18) {\n                        updateTempSetting(\"font_size\", 18);\n                        setTimeout(()=>saveTempSettings({\n                                silent: true\n                            }), 100);\n                    }\n                    break;\n            }\n            return;\n        }\n        // 单键快捷键\n        switch(e.key){\n            case \"ArrowLeft\":\n            case \"h\":\n                e.preventDefault();\n                goToPreviousChapter();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_15__[\"default\"].success(\"已切换到上一章\");\n                break;\n            case \"ArrowRight\":\n            case \"l\":\n                e.preventDefault();\n                goToNextChapter();\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_15__[\"default\"].success(\"已切换到下一章\");\n                break;\n            case \"f\":\n            case \"F\":\n                e.preventDefault();\n                toggleFullscreen();\n                break;\n            case \"s\":\n            case \"S\":\n                e.preventDefault();\n                setShowSettings(true);\n                break;\n            case \"b\":\n            case \"B\":\n                e.preventDefault();\n                setShowBookmarks(true);\n                break;\n            case \"t\":\n            case \"T\":\n                e.preventDefault();\n                toggleImmersiveTranslation();\n                break;\n            case \"r\":\n            case \"R\":\n                e.preventDefault();\n                setShowReadingStats(!showReadingStats);\n                break;\n            case \"m\":\n            case \"M\":\n                e.preventDefault();\n                // 切换阅读模式\n                const modes = [\n                    \"normal\",\n                    \"focus\",\n                    \"immersive\"\n                ];\n                const currentIndex = modes.indexOf(readingMode);\n                const nextMode = modes[(currentIndex + 1) % modes.length];\n                setReadingMode(nextMode);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_15__[\"default\"].success(\"已切换到\".concat(nextMode === \"normal\" ? \"标准\" : nextMode === \"focus\" ? \"专注\" : \"沉浸\", \"模式\"));\n                break;\n            case \"c\":\n            case \"C\":\n                e.preventDefault();\n                setShowChapterList(!showChapterList);\n                break;\n            case \"q\":\n            case \"Q\":\n                e.preventDefault();\n                setShowQuickActions(!showQuickActions);\n                break;\n            case \"Escape\":\n                e.preventDefault();\n                if (document.fullscreenElement) {\n                    document.exitFullscreen();\n                } else if (showSettings) {\n                    setShowSettings(false);\n                } else if (showBookmarks) {\n                    setShowBookmarks(false);\n                } else if (showTranslationHistory) {\n                    setShowTranslationHistory(false);\n                } else if (showTranslationSettings) {\n                    setShowTranslationSettings(false);\n                } else if (showChapterSummary) {\n                    setShowChapterSummary(false);\n                } else if (showChapterList) {\n                    setShowChapterList(false);\n                }\n                break;\n            case \"?\":\n                e.preventDefault();\n                setShowKeyboardHelp(true);\n                break;\n        }\n    };\n    // 滚动位置跟踪 - 增强版\n    const handleScroll = ()=>{\n        if (contentRef.current) {\n            const scrollTop = contentRef.current.scrollTop;\n            const scrollHeight = contentRef.current.scrollHeight;\n            const clientHeight = contentRef.current.clientHeight;\n            // 计算阅读位置百分比\n            const scrollPercentage = scrollTop / (scrollHeight - clientHeight);\n            const position = Math.round(scrollPercentage * 100);\n            // 更新阅读进度\n            updateProgress(position);\n            // 自动隐藏工具栏逼辑\n            if (autoHideToolbar) {\n                setShowToolbar(true);\n                if (toolbarTimeout) {\n                    clearTimeout(toolbarTimeout);\n                }\n                const timeout = setTimeout(()=>{\n                    setShowToolbar(false);\n                }, 3000);\n                setToolbarTimeout(timeout);\n            }\n        }\n    };\n    // 自动滚动功能\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (autoScroll && contentRef.current) {\n            const interval = setInterval(()=>{\n                if (contentRef.current) {\n                    const currentScroll = contentRef.current.scrollTop;\n                    const maxScroll = contentRef.current.scrollHeight - contentRef.current.clientHeight;\n                    if (currentScroll < maxScroll) {\n                        contentRef.current.scrollTop = currentScroll + scrollSpeed / 10;\n                    } else {\n                        // 自动跳转到下一章\n                        if (currentChapterIndex < allChapters.length - 1) {\n                            goToNextChapter();\n                        } else {\n                            setAutoScroll(false);\n                            react_hot_toast__WEBPACK_IMPORTED_MODULE_15__[\"default\"].success(\"已阅读完成！\");\n                        }\n                    }\n                }\n            }, 100);\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        autoScroll,\n        scrollSpeed,\n        currentChapterIndex,\n        allChapters.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && bookId && chapterId) {\n            fetchBookAndChapter();\n        }\n    }, [\n        user,\n        bookId,\n        chapterId\n    ]);\n    // 开始阅读计时\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (chapter) {\n            startReading();\n            return ()=>{\n                stopReading();\n            };\n        }\n    }, [\n        chapter,\n        startReading,\n        stopReading\n    ]);\n    // 添加滚动监听\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const contentElement = contentRef.current;\n        if (contentElement) {\n            contentElement.addEventListener(\"scroll\", handleScroll);\n            return ()=>{\n                contentElement.removeEventListener(\"scroll\", handleScroll);\n            };\n        }\n    }, [\n        chapter\n    ]);\n    // 添加键盘和鼠标事件监听\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        document.addEventListener(\"keydown\", handleKeyPress);\n        document.addEventListener(\"mousemove\", handleMouseMove);\n        // 监听全屏状态变化\n        const handleFullscreenChange = ()=>{\n            setIsFullscreen(!!document.fullscreenElement);\n        };\n        document.addEventListener(\"fullscreenchange\", handleFullscreenChange);\n        return ()=>{\n            document.removeEventListener(\"keydown\", handleKeyPress);\n            document.removeEventListener(\"mousemove\", handleMouseMove);\n            document.removeEventListener(\"fullscreenchange\", handleFullscreenChange);\n            if (toolbarTimeout) {\n                clearTimeout(toolbarTimeout);\n            }\n        };\n    }, [\n        autoHideToolbar,\n        toolbarTimeout\n    ]);\n    // 监听主题变化事件，强制重新渲染\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleThemeChange = (event)=>{\n            console.log(\"主题已更改:\", event.detail);\n            // 强制组件重新渲染以应用新样式\n            setForceUpdate((prev)=>prev + 1);\n        };\n        window.addEventListener(\"readerThemeChanged\", handleThemeChange);\n        return ()=>{\n            window.removeEventListener(\"readerThemeChanged\", handleThemeChange);\n        };\n    }, []);\n    if (!user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                lineNumber: 572,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n            lineNumber: 571,\n            columnNumber: 7\n        }, this);\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"bg-white shadow-sm border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_14___default()), {\n                                href: \"/dashboard/books/\".concat(bookId),\n                                className: \"flex items-center text-gray-500 hover:text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-5 w-5 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"返回书籍详情\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 582,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                        lineNumber: 581,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                    lineNumber: 580,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                        lineNumber: 594,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                    lineNumber: 593,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n            lineNumber: 579,\n            columnNumber: 7\n        }, this);\n    }\n    if (!chapter) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"bg-white shadow-sm border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center h-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_14___default()), {\n                                href: \"/dashboard/books/\".concat(bookId),\n                                className: \"flex items-center text-gray-500 hover:text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-5 w-5 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"返回书籍详情\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 605,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                        lineNumber: 604,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                    lineNumber: 603,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400 mb-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 617,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"章节不存在\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"该章节可能已被删除或您没有访问权限\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                    lineNumber: 616,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n            lineNumber: 602,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen transition-all duration-300 reader-container \".concat(isFullscreen ? \"bg-black\" : \"\"),\n        style: getThemeStyles(),\n        onMouseMove: handleMouseMove,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-500 ease-in-out \".concat(showToolbar || !autoHideToolbar ? \"translate-y-0\" : \"-translate-y-full\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/95 backdrop-blur-xl border-b border-gray-200/60 shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center h-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_14___default()), {\n                                            href: \"/dashboard/books/\".concat(bookId),\n                                            className: \"group flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100/70 rounded-xl transition-all duration-300 hover:scale-105\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 group-hover:-translate-x-0.5 transition-transform\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 648,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:block font-medium\",\n                                                    children: \"返回\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 649,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 w-px bg-gray-300 hidden sm:block\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowChapterList(!showChapterList),\n                                            className: \"group flex items-center px-4 py-2 rounded-xl transition-all duration-300 hover:scale-105 \".concat(showChapterList ? \"text-blue-600 bg-blue-50 hover:bg-blue-100\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-100/70\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2 group-hover:rotate-12 transition-transform\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:block font-medium\",\n                                                    children: \"目录\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 654,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden lg:flex items-center space-x-1 bg-gray-100 rounded-xl p-1\",\n                                            children: [\n                                                \"normal\",\n                                                \"focus\",\n                                                \"immersive\"\n                                            ].map((mode)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setReadingMode(mode),\n                                                    className: \"px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-200 \".concat(readingMode === mode ? \"bg-white text-blue-600 shadow-sm\" : \"text-gray-600 hover:text-gray-900 hover:bg-gray-50\"),\n                                                    children: mode === \"normal\" ? \"标准\" : mode === \"focus\" ? \"专注\" : \"沉浸\"\n                                                }, mode, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 669,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-center px-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-lg mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-base font-semibold text-gray-900 truncate\",\n                                                children: chapter.title\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-3 text-xs text-gray-500 mt-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-gray-100 px-2 py-0.5 rounded-full\",\n                                                        children: (_chapter_book = chapter.book) === null || _chapter_book === void 0 ? void 0 : _chapter_book.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"\\xb7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-blue-100 px-2 py-0.5 rounded-full text-blue-700\",\n                                                        children: [\n                                                            \"第 \",\n                                                            currentChapterIndex + 1,\n                                                            \" / \",\n                                                            allChapters.length,\n                                                            \" 章\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 695,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    progress && progress.percentage > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\xb7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-green-100 px-2 py-0.5 rounded-full text-green-700\",\n                                                                children: [\n                                                                    Math.round(progress.percentage),\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                lineNumber: 701,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden xl:flex items-center space-x-3 text-xs text-gray-500 bg-gradient-to-r from-gray-50 to-blue-50 px-4 py-2 rounded-xl border border-gray-200/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-3 w-3 text-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 715,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: getFormattedReadingTime()\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 714,\n                                                    columnNumber: 19\n                                                }, this),\n                                                saving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-3 w-3 animate-pulse text-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 720,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-600 font-medium\",\n                                                            children: \"保存中\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 719,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowReadingStats(!showReadingStats),\n                                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 713,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowTranslationHistory(true),\n                                                    className: \"p-2.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-xl transition-all duration-300 group\",\n                                                    title: \"翻译历史\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-4 w-4 group-hover:rotate-12 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: toggleImmersiveTranslation,\n                                                    className: \"p-2.5 rounded-xl transition-all duration-300 group \".concat(showImmersiveTranslation ? \"text-blue-600 bg-blue-100 hover:bg-blue-200\" : \"text-gray-500 hover:text-blue-600 hover:bg-blue-50\"),\n                                                    title: \"沉浸式翻译\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"h-4 w-4 group-hover:scale-110 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 751,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 742,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowChapterSummary(true),\n                                                    className: \"p-2.5 text-gray-500 hover:text-purple-600 hover:bg-purple-50 rounded-xl transition-all duration-300 group\",\n                                                    title: \"AI章节总结\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        className: \"h-4 w-4 group-hover:rotate-12 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 754,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowBookmarks(true),\n                                                    className: \"p-2.5 text-gray-500 hover:text-yellow-600 hover:bg-yellow-50 rounded-xl transition-all duration-300 group\",\n                                                    title: \"书签管理\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        className: \"h-4 w-4 group-hover:scale-110 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 762,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowSettings(true),\n                                                    className: \"p-2.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-xl transition-all duration-300 group\",\n                                                    title: \"阅读设置\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        className: \"h-4 w-4 group-hover:rotate-90 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 770,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 w-px bg-gray-300 mx-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: toggleFullscreen,\n                                                    className: \"p-2.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-xl transition-all duration-300 group\",\n                                                    title: isFullscreen ? \"退出全屏\" : \"全屏阅读\",\n                                                    children: isFullscreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                        className: \"h-4 w-4 group-hover:scale-90 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 786,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        className: \"h-4 w-4 group-hover:scale-110 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 787,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 780,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setAutoHideToolbar(!autoHideToolbar),\n                                                    className: \"p-2.5 rounded-xl transition-all duration-300 group \".concat(autoHideToolbar ? \"text-blue-600 bg-blue-100 hover:bg-blue-200\" : \"text-gray-500 hover:text-gray-700 hover:bg-gray-100\"),\n                                                    title: autoHideToolbar ? \"关闭自动隐藏\" : \"自动隐藏工具栏\",\n                                                    children: autoHideToolbar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                        className: \"h-4 w-4 group-hover:scale-90 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                        className: \"h-4 w-4 group-hover:scale-110 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 791,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowKeyboardHelp(true),\n                                                    className: \"p-2.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-xl transition-all duration-300 group\",\n                                                    title: \"快捷键帮助 (?)\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-4 w-4 group-hover:rotate-12 transition-transform\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 812,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 811,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 806,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                        lineNumber: 640,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                    lineNumber: 639,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                lineNumber: 634,\n                columnNumber: 7\n            }, this),\n             false && /*#__PURE__*/ 0,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"content-transition pt-14\",\n                style: {\n                    backgroundColor: \"var(--reader-bg)\",\n                    minHeight: \"100vh\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-1 bg-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-1 bg-blue-600 transition-all duration-300\",\n                            style: {\n                                width: \"\".concat((progress === null || progress === void 0 ? void 0 : progress.percentage) || 0, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 941,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                        lineNumber: 940,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\".concat(readingMode === \"focus\" ? \"max-w-4xl\" : readingMode === \"immersive\" ? \"max-w-7xl\" : \"max-w-6xl\", \" mx-auto transition-all duration-500 px-4\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: contentRef,\n                            className: \"reader-content-wrapper overflow-y-auto relative \".concat(readingMode === \"focus\" ? \"focus-mode\" : \"\", \" \").concat(lineHighlight ? \"line-highlight\" : \"\"),\n                            style: {\n                                height: isFullscreen ? \"100vh\" : \"calc(100vh - 100px)\",\n                                maxWidth: (currentSettings === null || currentSettings === void 0 ? void 0 : currentSettings.page_width) ? \"\".concat(currentSettings.page_width, \"px\") : \"1200px\",\n                                margin: \"0 auto\",\n                                padding: isFullscreen ? \"2rem 1.5rem\" : \"2rem 1.5rem 1rem\",\n                                scrollBehavior: \"smooth\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                    className: \"text-center mb-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl md:text-3xl font-medium mb-8 text-gray-900\",\n                                        style: {\n                                            fontSize: (currentSettings === null || currentSettings === void 0 ? void 0 : currentSettings.font_size) ? \"\".concat(Math.min(currentSettings.font_size + 8, 32), \"px\") : \"24px\",\n                                            fontFamily: \"var(--reader-font-family)\",\n                                            lineHeight: 1.3,\n                                            color: \"var(--reader-text)\"\n                                        },\n                                        children: chapter.title\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                        lineNumber: 969,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 968,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                    className: \"reader-article max-w-none\",\n                                    style: {\n                                        color: \"var(--reader-text)\",\n                                        fontSize: (currentSettings === null || currentSettings === void 0 ? void 0 : currentSettings.font_size) ? \"\".concat(currentSettings.font_size, \"px\") : \"18px\",\n                                        lineHeight: (currentSettings === null || currentSettings === void 0 ? void 0 : currentSettings.line_height) || 1.8,\n                                        fontFamily: (currentSettings === null || currentSettings === void 0 ? void 0 : currentSettings.font_family) || \"var(--reader-font-family)\",\n                                        textAlign: (currentSettings === null || currentSettings === void 0 ? void 0 : currentSettings.text_align) || \"left\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"chapter-content whitespace-pre-wrap\",\n                                        style: {\n                                            textIndent: \"2em\",\n                                            textRendering: \"optimizeLegibility\",\n                                            WebkitFontSmoothing: \"antialiased\",\n                                            MozOsxFontSmoothing: \"grayscale\"\n                                        },\n                                        children: chapter.content\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                        lineNumber: 993,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 983,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                                    className: \"mt-16 pt-8 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: goToPreviousChapter,\n                                                disabled: currentChapterIndex <= 0,\n                                                className: \"flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-40 disabled:cursor-not-allowed\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 1015,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"上一章\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                lineNumber: 1010,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_14___default()), {\n                                                href: \"/dashboard/books/\".concat(bookId),\n                                                className: \"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 1023,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"返回目录\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                lineNumber: 1019,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: goToNextChapter,\n                                                disabled: currentChapterIndex >= allChapters.length - 1,\n                                                className: \"flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-40 disabled:cursor-not-allowed\",\n                                                children: [\n                                                    \"下一章\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                        lineNumber: 1033,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                lineNumber: 1027,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                        lineNumber: 1009,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1007,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 954,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                        lineNumber: 950,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                lineNumber: 932,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed right-6 top-1/2 transform -translate-y-1/2 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setShowQuickActions(!showQuickActions),\n                    className: \"floating-button w-10 h-10 backdrop-blur-xl rounded-full shadow-xl border border-gray-200/50 flex items-center justify-center transition-all duration-300 hover:scale-110 \".concat(showQuickActions ? \"bg-blue-500 text-white border-blue-300\" : \"bg-white/95 text-gray-600 hover:text-blue-600 hover:bg-blue-50\"),\n                    title: showQuickActions ? \"隐藏快捷按钮\" : \"显示快捷按钮\",\n                    children: showQuickActions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"h-4 w-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M6 18L18 6M6 6l12 12\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1055,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                        lineNumber: 1054,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"h-4 w-4\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1059,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                        lineNumber: 1058,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                    lineNumber: 1044,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                lineNumber: 1043,\n                columnNumber: 7\n            }, this),\n            showQuickActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"floating-actions fixed right-20 top-1/2 transform -translate-y-1/2 z-40 space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"group relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"floating-button bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BookmarkButton__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    bookId: bookId,\n                                    chapterId: chapterId,\n                                    position: 0,\n                                    scrollPosition: ((_contentRef_current = contentRef.current) === null || _contentRef_current === void 0 ? void 0 : _contentRef_current.scrollTop) || 0,\n                                    className: \"!rounded-2xl !shadow-none !border-0 hover:bg-blue-50 transition-all duration-300 group-hover:scale-105\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1071,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 1070,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-1.5 rounded-lg text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none\",\n                                children: [\n                                    \"添加书签\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                        lineNumber: 1081,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 1079,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                        lineNumber: 1069,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"group relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    const modes = [\n                                        \"normal\",\n                                        \"focus\",\n                                        \"immersive\"\n                                    ];\n                                    const currentIndex = modes.indexOf(readingMode);\n                                    const nextMode = modes[(currentIndex + 1) % modes.length];\n                                    setReadingMode(nextMode);\n                                },\n                                className: \"floating-button w-14 h-14 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 flex items-center justify-center text-gray-600 hover:text-purple-600 hover:bg-purple-50 transition-all duration-300 group-hover:scale-105\",\n                                title: \"阅读模式\",\n                                children: readingMode === \"normal\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1097,\n                                    columnNumber: 43\n                                }, this) : readingMode === \"focus\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1098,\n                                    columnNumber: 42\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1098,\n                                    columnNumber: 72\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 1087,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-1.5 rounded-lg text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none\",\n                                children: [\n                                    readingMode === \"normal\" ? \"标准模式\" : readingMode === \"focus\" ? \"专注模式\" : \"沉浸模式\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                        lineNumber: 1102,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 1100,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                        lineNumber: 1086,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"group relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleImmersiveTranslation,\n                                className: \"floating-button w-14 h-14 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 flex items-center justify-center transition-all duration-300 group-hover:scale-105 \".concat(showImmersiveTranslation ? \"bg-gradient-to-r from-blue-500 to-purple-600 text-white border-blue-300\" : \"bg-white/95 text-gray-600 hover:text-blue-600 hover:bg-blue-50\"),\n                                title: \"沉浸式翻译\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                    className: \"h-6 w-6 group-hover:rotate-12 transition-transform\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1117,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 1108,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-1.5 rounded-lg text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none\",\n                                children: [\n                                    \"沉浸式翻译\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                        lineNumber: 1121,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 1119,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                        lineNumber: 1107,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"group relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setAutoScroll(!autoScroll),\n                                className: \"floating-button w-14 h-14 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 flex items-center justify-center transition-all duration-300 group-hover:scale-105 \".concat(autoScroll ? \"bg-gradient-to-r from-green-500 to-blue-500 text-white border-green-300\" : \"bg-white/95 text-gray-600 hover:text-green-600 hover:bg-green-50\"),\n                                title: \"自动滚动\",\n                                children: autoScroll ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                    className: \"h-6 w-6 group-hover:scale-110 transition-transform\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1137,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                    className: \"h-6 w-6 group-hover:scale-110 transition-transform\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1138,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 1127,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-1.5 rounded-lg text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none\",\n                                children: [\n                                    autoScroll ? \"停止自动滚动\" : \"开始自动滚动\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                        lineNumber: 1143,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 1141,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                        lineNumber: 1126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"group relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowSettings(true),\n                                className: \"floating-button w-14 h-14 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 flex items-center justify-center text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-300 group-hover:scale-105\",\n                                title: \"阅读设置\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                    className: \"h-6 w-6 group-hover:rotate-180 transition-transform duration-500\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1154,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 1149,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-900 text-white px-3 py-1.5 rounded-lg text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none\",\n                                children: [\n                                    \"阅读设置\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                        lineNumber: 1158,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 1156,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                        lineNumber: 1148,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                lineNumber: 1067,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"floating-navigation fixed left-6 top-1/2 transform -translate-y-1/2 z-40 \",\n                children: allChapters[currentChapterIndex] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-3 bg-gray-900/90 backdrop-blur-sm text-white px-3 py-2 rounded-xl text-xs max-w-48 shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium truncate\",\n                            children: allChapters[currentChapterIndex].title\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1171,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-300 mt-1\",\n                            children: [\n                                ((_chapter_word_count = chapter.word_count) === null || _chapter_word_count === void 0 ? void 0 : _chapter_word_count.toLocaleString()) || 0,\n                                \" 字 \\xb7 约 \",\n                                Math.ceil((chapter.word_count || 0) / 300),\n                                \" 分钟\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1174,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                    lineNumber: 1170,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                lineNumber: 1165,\n                columnNumber: 8\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ReaderSettingsPanel__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: showSettings,\n                onClose: ()=>setShowSettings(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                lineNumber: 1182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Translation__WEBPACK_IMPORTED_MODULE_11__.TranslationPopup, {\n                visible: showTranslation,\n                selectedText: selectedText,\n                position: selectionPosition,\n                onClose: hideTranslation,\n                bookId: bookId,\n                chapterId: chapterId,\n                textPosition: getTextPosition()\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                lineNumber: 1188,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Translation__WEBPACK_IMPORTED_MODULE_11__.TranslationHistory, {\n                visible: showTranslationHistory,\n                onClose: ()=>setShowTranslationHistory(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                lineNumber: 1199,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Translation__WEBPACK_IMPORTED_MODULE_11__.TranslationSettings, {\n                visible: showTranslationSettings,\n                onClose: ()=>setShowTranslationSettings(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                lineNumber: 1205,\n                columnNumber: 7\n            }, this),\n            showBookmarks && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"书签列表\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1215,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowBookmarks(false),\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-6 w-6\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1221,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                        lineNumber: 1220,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1216,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1214,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 overflow-y-auto max-h-[60vh]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BookmarkList__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                bookId: bookId,\n                                onBookmarkClick: handleBookmarkJump\n                            }, void 0, false, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 1226,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1225,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                    lineNumber: 1213,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                lineNumber: 1212,\n                columnNumber: 9\n            }, this),\n            showKeyboardHelp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-gray-200/50 max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-gray-200/50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold text-gray-900 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-5 w-5 mr-3 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                lineNumber: 1242,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"快捷键指南\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                        lineNumber: 1241,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowKeyboardHelp(false),\n                                        className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-xl transition-all duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1249,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                        lineNumber: 1245,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 1240,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1239,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 overflow-y-auto max-h-96\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-semibold text-gray-900 mb-4 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2 text-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1258,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"导航操作\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 1257,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: \"上一章\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1263,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                            className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                            children: \"←\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                            lineNumber: 1265,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                            className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                            children: \"H\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                            lineNumber: 1266,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1264,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: \"下一章\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1270,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                            className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                            children: \"→\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                            lineNumber: 1272,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                            className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                            children: \"L\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                            lineNumber: 1273,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1271,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1269,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: \"章节目录\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1277,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                    className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                    children: \"C\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1278,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1276,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 1261,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1256,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-semibold text-gray-900 mb-4 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2 text-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1285,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"设置操作\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 1284,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: \"阅读设置\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1290,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                    className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                    children: \"S\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1291,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1289,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: \"全屏模式\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1294,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                    className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                    children: \"F\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1295,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1293,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: \"阅读模式\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1298,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                    className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                    children: \"M\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1299,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1297,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 1288,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1283,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-semibold text-gray-900 mb-4 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2 text-yellow-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1306,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"书签操作\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 1305,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: \"书签列表\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1311,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                    className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                    children: \"B\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1312,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1310,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: \"阅读统计\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1315,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                    className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                    children: \"R\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1316,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1314,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: \"快捷按钮\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1319,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                    className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                    children: \"Q\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1320,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1318,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 1309,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1304,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-semibold text-gray-900 mb-4 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2 text-purple-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1327,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"翻译功能\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 1326,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: \"沉浸式翻译\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1332,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                    className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                    children: \"T\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1333,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1331,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: \"增大字体\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1336,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                            className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                            children: \"Ctrl\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                            lineNumber: 1338,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                            className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                            children: \"+\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                            lineNumber: 1339,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1337,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1335,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: \"减小字体\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1343,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                            className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                            children: \"Ctrl\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                            lineNumber: 1345,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                            className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                            children: \"-\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                            lineNumber: 1346,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1344,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1342,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-700\",\n                                                                    children: \"重置字体\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1350,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                            className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                            children: \"Ctrl\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                            lineNumber: 1352,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                                            className: \"px-2 py-1 bg-gray-100 rounded text-xs\",\n                                                                            children: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                            lineNumber: 1353,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                                    lineNumber: 1351,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                            lineNumber: 1349,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 1330,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1325,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1255,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6 p-4 bg-blue-50 rounded-xl border border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 1362,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-blue-900\",\n                                                    children: \"提示\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 1363,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1361,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-800\",\n                                            children: [\n                                                \"按 \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                                    className: \"px-1 py-0.5 bg-blue-200 rounded text-xs\",\n                                                    children: \"?\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                                    lineNumber: 1366,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \" 可以随时查看此帮助面板。 在阅读过程中，您可以使用这些快捷键来提高阅读效率。\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1365,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1360,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1254,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-t border-gray-200/50 bg-gray-50/50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowKeyboardHelp(false),\n                                className: \"w-full px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl transition-all duration-200 font-medium\",\n                                children: \"了解了，开始阅读\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 1373,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1372,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                    lineNumber: 1238,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                lineNumber: 1237,\n                columnNumber: 9\n            }, this),\n            showReadingStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 p-4 min-w-80 fade-in-scale\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-gray-900 flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1390,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"阅读统计\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1389,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowReadingStats(false),\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                        lineNumber: 1397,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1393,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1388,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-3 bg-blue-50 rounded-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xl font-bold text-blue-600\",\n                                            children: getFormattedReadingTime()\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1402,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-blue-700\",\n                                            children: \"阅读时间\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1403,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1401,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-3 bg-green-50 rounded-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xl font-bold text-green-600\",\n                                            children: [\n                                                Math.round((progress === null || progress === void 0 ? void 0 : progress.percentage) || 0),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1406,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-green-700\",\n                                            children: \"章节进度\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1407,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1405,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-3 bg-purple-50 rounded-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xl font-bold text-purple-600\",\n                                            children: (chapter === null || chapter === void 0 ? void 0 : (_chapter_word_count1 = chapter.word_count) === null || _chapter_word_count1 === void 0 ? void 0 : _chapter_word_count1.toLocaleString()) || 0\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1410,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-purple-700\",\n                                            children: \"章节字数\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1411,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1409,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-3 bg-orange-50 rounded-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xl font-bold text-orange-600\",\n                                            children: Math.ceil(((chapter === null || chapter === void 0 ? void 0 : chapter.word_count) || 0) / (totalReadingTime / 60000 || 1))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1414,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-orange-700\",\n                                            children: \"阅读速度(字/分)\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                            lineNumber: 1415,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1413,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1400,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 flex justify-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setWordHighlight(!wordHighlight),\n                                    className: \"px-3 py-1.5 text-xs rounded-lg transition-all duration-200 \".concat(wordHighlight ? \"bg-blue-500 text-white\" : \"bg-gray-100 text-gray-600 hover:bg-gray-200\"),\n                                    children: \"字词高亮\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1419,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setLineHighlight(!lineHighlight),\n                                    className: \"px-3 py-1.5 text-xs rounded-lg transition-all duration-200 \".concat(lineHighlight ? \"bg-blue-500 text-white\" : \"bg-gray-100 text-gray-600 hover:bg-gray-200\"),\n                                    children: \"行高亮\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1427,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1418,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                    lineNumber: 1387,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                lineNumber: 1386,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 right-6 z-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl border border-gray-200/50 p-3 hover:shadow-2xl transition-all duration-300\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                const currentSize = (currentSettings === null || currentSettings === void 0 ? void 0 : currentSettings.font_size) || 18;\n                                const newSize = Math.max(12, currentSize - 2);\n                                if (newSize !== currentSize) {\n                                    updateTempSetting(\"font_size\", newSize);\n                                    // 静默保存设置，不显示提示\n                                    setTimeout(()=>{\n                                        saveTempSettings({\n                                            silent: true\n                                        });\n                                    }, 100);\n                                }\n                            },\n                            className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-xl transition-all duration-200 hover:scale-105\",\n                            title: \"减小字体 (Ctrl+-)\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 1458,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1443,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-700 font-semibold min-w-16 text-center bg-gradient-to-r from-blue-50 to-indigo-50 px-3 py-2 rounded-lg border border-blue-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-600\",\n                                    children: (currentSettings === null || currentSettings === void 0 ? void 0 : currentSettings.font_size) || 18\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1462,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500\",\n                                    children: \"px\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                    lineNumber: 1463,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1461,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                const currentSize = (currentSettings === null || currentSettings === void 0 ? void 0 : currentSettings.font_size) || 18;\n                                const newSize = Math.min(36, currentSize + 2);\n                                if (newSize !== currentSize) {\n                                    updateTempSetting(\"font_size\", newSize);\n                                    // 静默保存设置，不显示提示\n                                    setTimeout(()=>{\n                                        saveTempSettings({\n                                            silent: true\n                                        });\n                                    }, 100);\n                                }\n                            },\n                            className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-xl transition-all duration-200 hover:scale-105\",\n                            title: \"增大字体 (Ctrl++)\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 1481,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1466,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-6 w-px bg-gray-300\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1484,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                const defaultSize = 18;\n                                const currentSize = (currentSettings === null || currentSettings === void 0 ? void 0 : currentSettings.font_size) || 18;\n                                if (currentSize !== defaultSize) {\n                                    updateTempSetting(\"font_size\", defaultSize);\n                                    // 静默保存设置，不显示提示\n                                    setTimeout(()=>{\n                                        saveTempSettings({\n                                            silent: true\n                                        });\n                                    }, 100);\n                                }\n                            },\n                            className: \"p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-xl transition-all duration-200 hover:scale-105\",\n                            title: \"重置字体大小 (Ctrl+0)\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BookOpen_Bookmark_ChevronLeft_ChevronRight_Clock_Coffee_Eye_EyeOff_History_Home_Languages_List_Maximize2_Minimize2_MousePointer2_Pause_Play_RotateCcw_Save_Search_Settings_Sidebar_Type_X_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                                lineNumber: 1501,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                            lineNumber: 1486,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                    lineNumber: 1442,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                lineNumber: 1441,\n                columnNumber: 7\n            }, this),\n            showImmersiveTranslation && chapter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Translation__WEBPACK_IMPORTED_MODULE_11__.ImmersiveTranslation, {\n                enabled: showImmersiveTranslation,\n                content: chapter.content,\n                bookId: bookId,\n                chapterId: chapterId,\n                splitRatio: (translationSettings === null || translationSettings === void 0 ? void 0 : translationSettings.immersive_split_ratio) || 0.5,\n                autoTranslate: (translationSettings === null || translationSettings === void 0 ? void 0 : translationSettings.immersive_auto_translate) || false,\n                targetLanguage: (translationSettings === null || translationSettings === void 0 ? void 0 : translationSettings.default_target_language) || \"zh-CN\",\n                sourceLanguage: (translationSettings === null || translationSettings === void 0 ? void 0 : translationSettings.default_source_language) || \"auto\",\n                onToggle: toggleImmersiveTranslation\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                lineNumber: 1508,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChapterSummary__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                visible: showChapterSummary,\n                onClose: ()=>setShowChapterSummary(false),\n                bookId: bookId,\n                chapterId: chapterId,\n                chapterTitle: chapter.title\n            }, void 0, false, {\n                fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n                lineNumber: 1522,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\augment-test\\\\util\\\\BookRealm\\\\frontend\\\\src\\\\app\\\\dashboard\\\\reader\\\\[bookId]\\\\[chapterId]\\\\page.tsx\",\n        lineNumber: 626,\n        columnNumber: 5\n    }, this);\n}\n_s(ReaderPage, \"vu6nMY0jrG6fENkWkA6dLAffrSc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useTextSelection__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        _hooks_useReadingProgress__WEBPACK_IMPORTED_MODULE_5__.useReadingProgress,\n        _hooks_useReaderSettings__WEBPACK_IMPORTED_MODULE_6__.useReaderSettings,\n        _hooks_useBookmarks__WEBPACK_IMPORTED_MODULE_7__.useBookmarks\n    ];\n});\n_c = ReaderPage;\nvar _c;\n$RefreshReg$(_c, \"ReaderPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/reader/[bookId]/[chapterId]/page.tsx\n"));

/***/ })

});