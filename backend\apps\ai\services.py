"""
AI服务核心模块
"""

import json
import time
import logging
from typing import Dict, List, Any, Optional
from openai import OpenAI
from django.conf import settings

from .config import AIConfig
from .exceptions import (
    AIServiceError, AIQuotaExceededError, AITimeoutError,
    AIInvalidResponseError, AIModelNotAvailableError
)

logger = logging.getLogger(__name__)


class AIService:
    """章节汇总\u670d务核心类"""
    
    def __init__(self):
        self.config = AIConfig.get_openai_config()
        self.chapter_config = AIConfig.get_chapter_detection_config()
        self.prompt_config = AIConfig.get_prompt_config()
        
        # 检查是否使用模拟模式
        self.mock_mode = self.config.get('mock_mode', False)
        
        if self.mock_mode:
            logger.info("无AI模拟模式已启用")
            self.client = None
        else:
            if not self.config['api_key']:
                raise AIServiceError("OpenAI API密钥未配置")
            
            self.client = OpenAI(
                api_key=self.config['api_key'],
                base_url=self.config['base_url'],
                timeout=self.config['timeout']
            )
    
    def detect_chapters(self, text: str) -> List[Dict[str, Any]]:
        """
        使用AI检测章节分割点

        Args:
            text: 要分析的文本内容

        Returns:
            章节列表，每个章节包含title, start_position, end_position等信息
        """
        if not text.strip():
            return []

        # 检查文本长度
        if len(text) > self.chapter_config['max_text_length']:
            return self._detect_chapters_segmented(text)
        else:
            return self._detect_chapters_single(text)

    def translate_text(self, text: str, target_lang: str = 'zh-CN',
                      source_lang: str = 'auto', context: str = '') -> Dict[str, Any]:
        """
        使用AI翻译文本

        Args:
            text: 要翻译的文本
            target_lang: 目标语言 (zh-CN, en, ja, ko, etc.)
            source_lang: 源语言 (auto为自动检测)
            context: 上下文信息，帮助提高翻译准确性

        Returns:
            翻译结果字典，包含翻译文本、检测到的源语言等信息
        """
        if not text.strip():
            return {'success': False, 'error': '文本为空'}

        try:
            # 构建翻译提示词
            prompt = self._build_translation_prompt(text, source_lang, target_lang, context)

            # 计算合理的max_tokens值
            # 确保至少有200个token来生成完整的JSON响应
            min_tokens = 200  # 基础token数，用于JSON格式和元数据
            text_tokens = len(text) * 3  # 翻译结果通常比原文长
            calculated_tokens = max(min_tokens, text_tokens)
            final_max_tokens = min(4000, calculated_tokens)  # 增加上限到4000
            
            logger.info(f"[翻译API] Token计算: 原文长度={len(text)}, 计算tokens={calculated_tokens}, 最终max_tokens={final_max_tokens}")
            
            response = self._call_openai_with_retry(
                messages=[
                    {"role": "system", "content": "你是一个专业的翻译助手，擅长文学翻译和上下文理解。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=final_max_tokens
            )

            # 解析翻译结果
            return self._parse_translation_response(response, text, source_lang, target_lang)

        except Exception as e:
            logger.error(f"AI翻译失败: {e}")
            return {
                'success': False,
                'error': f'翻译失败: {str(e)}',
                'original_text': text
            }
    
    def _detect_chapters_single(self, text: str) -> List[Dict[str, Any]]:
        """对单个文本段进行章节检测"""
        try:
            prompt = self.prompt_config['chapter_detection'].format(text=text)
            
            response = self._call_openai_with_retry(
                messages=[
                    {"role": "system", "content": "你是一个专业的文本分析助手。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.chapter_config['temperature'],
                max_tokens=self.chapter_config['max_tokens']
            )
            
            # 解析响应
            result = self._parse_chapter_response(response)
            return self._validate_chapters(result, len(text))
            
        except Exception as e:
            logger.error(f"AI章节检测失败: {e}")
            raise AIServiceError(f"章节检测失败: {str(e)}")
    
    def _detect_chapters_segmented(self, text: str) -> List[Dict[str, Any]]:
        """对长文本进行分段检测"""
        segment_length = self.chapter_config['segment_length']
        overlap = self.chapter_config['segment_overlap']
        
        segments = []
        start = 0
        
        while start < len(text):
            end = min(start + segment_length, len(text))
            segment = text[start:end]
            
            # 如果不是最后一段，尝试在合适的位置断开
            if end < len(text):
                # 寻找段落边界
                last_paragraph = segment.rfind('\n\n')
                if last_paragraph > segment_length * 0.8:  # 至少保留80%的内容
                    end = start + last_paragraph
                    segment = text[start:end]
            
            segments.append({
                'text': segment,
                'start_offset': start,
                'end_offset': end
            })
            
            start = end - overlap if end < len(text) else end
        
        # 分别检测每个段落的章节
        all_chapters = []
        for segment_info in segments:
            try:
                segment_chapters = self._detect_chapters_single(segment_info['text'])
                
                # 调整章节位置偏移
                for chapter in segment_chapters:
                    chapter['start_position'] += segment_info['start_offset']
                    chapter['end_position'] += segment_info['start_offset']
                
                all_chapters.extend(segment_chapters)
                
            except Exception as e:
                logger.warning(f"段落章节检测失败: {e}")
                continue
        
        # 合并和去重章节
        return self._merge_segmented_chapters(all_chapters, len(text))
    
    def _call_openai_with_retry(self, messages: List[Dict], **kwargs) -> str:
        """带重试机制的OpenAI API调用"""
        max_retries = self.config['max_retries']

        for attempt in range(max_retries + 1):
            try:
                logger.info(f"[翻译API] 第{attempt+1}次调用OpenAI API")
                logger.info(f"[翻译API] 请求参数: model={self.config['model']}, kwargs={kwargs}")
                logger.info(f"[翻译API] messages数量: {len(messages)}")
                
                response = self.client.chat.completions.create(
                    model=self.config['model'],
                    messages=messages,
                    **kwargs
                )
                
                logger.info(f"[翻译API] 响应类型: {type(response)}")
                if hasattr(response, '__dict__'):
                    logger.info(f"[翻译API] 响应属性: {list(response.__dict__.keys())}")

                # 处理不同API端点的响应格式
                if hasattr(response, 'choices') and response.choices:
                    choice = response.choices[0]
                    content = choice.message.content
                    
                    # 检查响应是否被截断
                    finish_reason = getattr(choice, 'finish_reason', None)
                    logger.info(f"[翻译API] finish_reason: {finish_reason}")
                    
                    if finish_reason == 'length':
                        logger.warning(f"[翻译API] 响应因token限制被截断！")
                    elif finish_reason == 'content_filter':
                        logger.warning(f"[翻译API] 响应被内容过滤器阻止！")
                    elif finish_reason != 'stop':
                        logger.warning(f"[翻译API] 异常的finish_reason: {finish_reason}")
                    
                    logger.info(f"[翻译API] 提取到的内容长度: {len(content) if content else 0}")
                    logger.info(f"[翻试API] 内容是否以大括号结尾: {content.rstrip().endswith('}') if content else False}")
                    return content.strip() if content else ""
                elif isinstance(response, str):
                    # 某些API端点直接返回字符串
                    logger.info(f"[翻译API] 字符串响应长度: {len(response)}")
                    return response.strip()
                elif isinstance(response, dict):
                    # 处理字典格式的响应
                    if 'choices' in response and response['choices']:
                        content = response['choices'][0]['message']['content']
                        logger.info(f"[翻译API] 字典响应内容长度: {len(content) if content else 0}")
                        return content.strip() if content else ""
                    elif 'content' in response:
                        return response['content'].strip()
                    elif 'text' in response:
                        return response['text'].strip()

                raise ValueError(f"无法解析API响应格式: {type(response)}")
                
            except Exception as e:
                error_msg = str(e).lower()
                
                if 'quota' in error_msg or 'billing' in error_msg:
                    raise AIQuotaExceededError("OpenAI配额已用完")
                elif 'timeout' in error_msg:
                    raise AITimeoutError("OpenAI请求超时")
                elif 'model' in error_msg and 'not found' in error_msg:
                    raise AIModelNotAvailableError(f"模型不可用: {self.config['model']}")
                
                if attempt == max_retries:
                    raise AIServiceError(f"OpenAI API调用失败: {str(e)}")
                
                # 指数退避
                time.sleep(2 ** attempt)
    
    def _parse_chapter_response(self, response: str) -> List[Dict[str, Any]]:
        """解析AI响应的章节信息"""
        try:
            # 尝试提取JSON部分
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx == -1 or end_idx == 0:
                raise ValueError("响应中未找到JSON格式数据")
            
            json_str = response[start_idx:end_idx]
            data = json.loads(json_str)
            
            if 'chapters' not in data:
                raise ValueError("响应中缺少chapters字段")
            
            return data['chapters']
            
        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"解析AI响应失败: {e}, 响应内容: {response}")
            raise AIInvalidResponseError(f"AI响应格式无效: {str(e)}")
    
    def _validate_chapters(self, chapters: List[Dict], text_length: int) -> List[Dict[str, Any]]:
        """验证和修正章节信息"""
        if not chapters:
            return []
        
        validated = []
        for i, chapter in enumerate(chapters):
            # 确保必要字段存在
            if 'start_position' not in chapter or 'end_position' not in chapter:
                continue
            
            start = max(0, int(chapter['start_position']))
            end = min(text_length, int(chapter['end_position']))
            
            # 确保章节有效
            if end <= start or end - start < self.chapter_config['min_chapter_length']:
                continue
            
            validated.append({
                'title': chapter.get('title', f'第{i+1}章'),
                'start_position': start,
                'end_position': end,
                'order_index': i + 1,
                'reason': chapter.get('reason', ''),
                'word_count': end - start
            })
        
        # 确保章节连续且不重叠
        return self._fix_chapter_boundaries(validated, text_length)
    
    def _fix_chapter_boundaries(self, chapters: List[Dict], text_length: int) -> List[Dict[str, Any]]:
        """修正章节边界，确保连续且不重叠"""
        if not chapters:
            return []
        
        # 按开始位置排序
        chapters.sort(key=lambda x: x['start_position'])
        
        fixed = []
        for i, chapter in enumerate(chapters):
            if i == 0:
                # 第一章从0开始
                chapter['start_position'] = 0
            else:
                # 后续章节从前一章结束位置开始
                chapter['start_position'] = fixed[-1]['end_position']
            
            if i == len(chapters) - 1:
                # 最后一章到文本结尾
                chapter['end_position'] = text_length
            
            # 重新计算字数
            chapter['word_count'] = chapter['end_position'] - chapter['start_position']
            
            fixed.append(chapter)
        
        return fixed
    
    def _merge_segmented_chapters(self, chapters: List[Dict], text_length: int) -> List[Dict[str, Any]]:
        """合并分段检测的章节结果"""
        if not chapters:
            return []
        
        # 按位置排序并去重
        chapters.sort(key=lambda x: x['start_position'])
        
        merged = []
        for chapter in chapters:
            # 检查是否与已有章节重叠
            overlap = False
            for existing in merged:
                if (chapter['start_position'] < existing['end_position'] and 
                    chapter['end_position'] > existing['start_position']):
                    overlap = True
                    break
            
            if not overlap:
                merged.append(chapter)
        
        return self._fix_chapter_boundaries(merged, text_length)

    def _build_translation_prompt(self, text: str, source_lang: str, target_lang: str, context: str) -> str:
        """构建翻译提示词"""
        # 语言映射
        lang_names = {
            'zh-CN': '中文',
            'en': '英文',
            'ja': '日文',
            'ko': '韩文',
            'fr': '法文',
            'de': '德文',
            'es': '西班牙文',
            'auto': '自动检测'
        }

        source_name = lang_names.get(source_lang, source_lang)
        target_name = lang_names.get(target_lang, target_lang)

        prompt = f"""请将以下文本翻译成{target_name}。

原文：{text}

翻译要求：
1. 保持原文的语调和风格
2. 确保翻译的准确性和流畅性
3. 对于文学作品，注意保持文学性和美感
4. 专有名词保持一致性
5. 如果是对话，保持对话的自然性"""

        if context:
            prompt += f"\n\n上下文信息：{context}"

        if source_lang == 'auto':
            prompt += f"\n\n请先识别源语言，然后翻译成{target_name}。"

        prompt += "\n\n请以JSON格式返回结果：\n"
        prompt += '{"translation": "翻译结果", "detected_language": "检测到的源语言代码", "confidence": 0.95}'

        return prompt

    def _parse_translation_response(self, response: str, original_text: str,
                                  source_lang: str, target_lang: str) -> Dict[str, Any]:
        """解析翻译响应"""
        try:
            # 记录原始响应用于调试
            logger.info(f"[翻译响应] 原始响应长度: {len(response)}")
            logger.info(f"[翻译响应] 原始响应内容: {repr(response)}")
            logger.info(f"[翻译响应] 原文: {repr(original_text[:50])}...")
            
            # 尝试解析JSON格式
            import json

            # 查找JSON部分
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            logger.info(f"[翻译响应] JSON位置: start={start_idx}, end={end_idx}")

            if start_idx != -1 and end_idx > start_idx:
                json_str = response[start_idx:end_idx]
                logger.info(f"[翻译响应] 提取的JSON字符串: {repr(json_str)}")
                
                # 检查JSON字符串是否完整
                if not json_str.endswith('}'):
                    logger.warning(f"[翻译响应] JSON可能不完整: {repr(json_str)}")
                    # 尝试修复不完整的JSON
                    if json_str.count('{') > json_str.count('}'):
                        # 检查是否在引号内被截断
                        if json_str.count('"') % 2 == 1:
                            json_str += '"'
                        json_str += '}'
                        logger.info(f"[翻译响应] 尝试修复JSON: {repr(json_str)}")
                    # 尝试查找更完整的JSON（向前搜索）
                    elif '{' in response and '}' in response:
                        # 重新查找最后一个完整的JSON
                        last_brace = response.rfind('}')
                        if last_brace > start_idx:
                            json_str = response[start_idx:last_brace + 1]
                            logger.info(f"[翻译响应] 重新提取JSON: {repr(json_str)}")
                
                try:
                    data = json.loads(json_str)
                    logger.info(f"[翻译响应] 解析成功，数据键: {list(data.keys())}")
                    
                    translation_result = data.get('translation', '').strip()
                    logger.info(f"[翻译响应] 翻译结果: {repr(translation_result)}")
                    
                    return {
                        'success': True,
                        'translation': translation_result,
                        'original_text': original_text,
                        'detected_language': data.get('detected_language', source_lang),
                        'target_language': target_lang,
                        'confidence': data.get('confidence', 0.9)
                    }
                except json.JSONDecodeError as json_err:
                    logger.error(f"[翻译响应] JSON解析失败: {json_err}，JSON字符串: {repr(json_str)}")
                    # 回退到使用原始响应
                    return {
                        'success': True,
                        'translation': response.strip(),
                        'original_text': original_text,
                        'detected_language': source_lang,
                        'target_language': target_lang,
                        'confidence': 0.6
                    }
            else:
                # 如果没有JSON格式，直接使用响应作为翻译结果
                logger.warning(f"[翻译响应] 未找到JSON格式，使用原始响应: {repr(response)}")
                return {
                    'success': True,
                    'translation': response.strip(),
                    'original_text': original_text,
                    'detected_language': source_lang,
                    'target_language': target_lang,
                    'confidence': 0.8
                }

        except (json.JSONDecodeError, Exception) as e:
            logger.error(f"[翻译响应] 解析翻译响应失败: {e}，原始响应: {repr(response)}")
            return {
                'success': True,
                'translation': response.strip(),
                'original_text': original_text,
                'detected_language': source_lang,
                'target_language': target_lang,
                'confidence': 0.7
            }

    def detect_language(self, text: str) -> str:
        """简单的语言检测"""
        import re

        # 检测中文字符
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        # 检测英文字符
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        # 检测日文字符
        japanese_chars = len(re.findall(r'[\u3040-\u309f\u30a0-\u30ff]', text))
        # 检测韩文字符
        korean_chars = len(re.findall(r'[\uac00-\ud7af]', text))

        total_chars = len(text)
        if total_chars == 0:
            return 'auto'

        # 计算各语言字符占比
        chinese_ratio = chinese_chars / total_chars
        english_ratio = english_chars / total_chars
        japanese_ratio = japanese_chars / total_chars
        korean_ratio = korean_chars / total_chars

        # 根据占比判断语言
        if chinese_ratio > 0.3:
            return 'zh-CN'
        elif english_ratio > 0.5:
            return 'en'
        elif japanese_ratio > 0.3:
            return 'ja'
        elif korean_ratio > 0.3:
            return 'ko'
        else:
            return 'auto'

    def summarize_chapter(self, chapter_title: str, chapter_content: str, language: str = 'zh-CN') -> Dict[str, Any]:
        """
        使用AI对章节内容进行总结

        Args:
            chapter_title: 章节标题
            chapter_content: 章节内容
            language: 目标语言

        Returns:
            包含总结结果的字典
        """
        if not chapter_content.strip():
            return {
                'success': False,
                'error': '章节内容为空'
            }

        try:
            logger.info(f"开始总结章节: {chapter_title} (模拟模式: {self.mock_mode})")
            
            if self.mock_mode:
                # 模拟模式：返回模拟的总结数据
                import time
                time.sleep(2)  # 模拟处理时间
                
                mock_summary = self._generate_mock_summary(chapter_title, chapter_content, language)
                logger.info(f"模拟章节总结完成: {chapter_title}")
                
                return {
                    'success': True,
                    'summary': mock_summary,
                    'chapter_title': chapter_title,
                    'word_count': len(chapter_content),
                    'summary_word_count': len(mock_summary),
                    'language': language
                }
            else:
                # 真实AI模式
                summary_prompt = self._build_summary_prompt(chapter_title, chapter_content, language)

                response = self.client.chat.completions.create(
                    model=self.config['model'],
                    messages=[
                        {
                            "role": "system",
                            "content": "你是一个专业的文本总结助手，擅长提取文章的核心内容并生成结构化的总结。"
                        },
                        {
                            "role": "user",
                            "content": summary_prompt
                        }
                    ],
                    temperature=0.3,
                    max_tokens=2000
                )

                summary_text = response.choices[0].message.content.strip()
                logger.info(f"章节总结完成: {chapter_title}")

                return {
                    'success': True,
                    'summary': summary_text,
                    'chapter_title': chapter_title,
                    'word_count': len(chapter_content),
                    'summary_word_count': len(summary_text),
                    'language': language
                }

        except Exception as e:
            logger.error(f"章节总结失败: {str(e)}")
            return {
                'success': False,
                'error': f'总结生成失败: {str(e)}'
            }

    def _build_summary_prompt(self, title: str, content: str, language: str) -> str:
        """构建章节总结的提示词"""

        # 根据语言选择提示词模板
        if language == 'zh-CN':
            prompt_template = """
请对以下章节内容进行详细总结，并按照指定的Markdown格式返回：

**章节标题：** {title}

**章节内容：**
{content}

请按照以下Markdown格式返回总结：

# 📖 章节总结

## 🎯 核心要点
- [要点1：用一句话概括最重要的内容]
- [要点2：第二重要的内容]
- [要点3：第三重要的内容]

## 📝 详细概述
[用2-3段话详细描述章节的主要内容，包括关键情节、人物发展、重要信息等]

## 👥 关键人物
- **[人物名]**：[该人物在本章节中的作用和发展]
- **[人物名]**：[该人物在本章节中的作用和发展]

## 🔍 重要细节
- [重要的背景信息或细节1]
- [重要的背景信息或细节2]
- [重要的背景信息或细节3]

## 💭 章节意义
[分析这个章节在整个故事中的作用和意义，以及对后续情节的影响]

## 🏷️ 关键词
`[关键词1]` `[关键词2]` `[关键词3]` `[关键词4]` `[关键词5]`

---
*总结字数：约[X]字 | 原文字数：约{word_count}字*
"""
        else:
            prompt_template = """
Please provide a detailed summary of the following chapter content in Markdown format:

**Chapter Title:** {title}

**Chapter Content:**
{content}

Please return the summary in the following Markdown format:

# 📖 Chapter Summary

## 🎯 Key Points
- [Point 1: Most important content in one sentence]
- [Point 2: Second most important content]
- [Point 3: Third most important content]

## 📝 Detailed Overview
[2-3 paragraphs describing the main content of the chapter, including key plot points, character development, important information, etc.]

## 👥 Key Characters
- **[Character Name]**: [Role and development of this character in this chapter]
- **[Character Name]**: [Role and development of this character in this chapter]

## 🔍 Important Details
- [Important background information or detail 1]
- [Important background information or detail 2]
- [Important background information or detail 3]

## 💭 Chapter Significance
[Analysis of the role and significance of this chapter in the overall story, and its impact on subsequent plot]

## 🏷️ Keywords
`[keyword1]` `[keyword2]` `[keyword3]` `[keyword4]` `[keyword5]`

---
*Summary word count: ~[X] words | Original word count: ~{word_count} words*
"""

        return prompt_template.format(
            title=title,
            content=content[:4000],  # 限制内容长度避免token超限
            word_count=len(content)
        )

    def _generate_mock_summary(self, chapter_title: str, chapter_content: str, language: str) -> str:
        """生成模拟总结数据"""
        content_preview = chapter_content[:200] + "..." if len(chapter_content) > 200 else chapter_content
        word_count = len(chapter_content)
        
        if language == 'zh-CN':
            mock_summary = f"""
# 📝 章节总结

## 🎯 核心要点
- 本章介绍了「{chapter_title}」的主要内容和情节发展
- 章节中的关键信息和重要转折点得到了充分展示
- 为后续情节的发展做了铺垫

## 📝 详细概述
这一章节主要讲述了{chapter_title}的核心内容。通过精心的情节设计和人物塑造，作者成功地推进了故事的发展。

在这一章节中，读者可以看到主要人物的成长和变化，以及整个故事情节的进一步深入。这不仅丰富了故事的层次，也为读者带来了更加深入的阅读体验。

## 👥 关键人物
- **主要人物**：在这一章节中起到了推动情节发展的作用
- **次要人物**：为故事增添了丰富的层次和深度

## 🔍 重要细节
- 章节中包含了关键的背景信息和情节元素
- 作者通过精彩的描述展示了主要场景和情境
- 这一章节为整个故事提供了重要的信息支撑

## 💭 章节意义
这一章节在整个故事中占据重要位置，不仅承接了前面的情节，也为后面的发展做出了铺垫。通过这一章节，读者可以更好地理解故事的核心主题和人物关系。

## 🏷️ 关键词
`{chapter_title}` `情节发展` `人物塑造` `故事推进` `主题表达`

---
*总结字数：约350字 | 原文字数：约{word_count}字*
            """.strip()
        else:
            mock_summary = f"""
# 📝 Chapter Summary

## 🎯 Key Points
- This chapter introduces the main content and plot development of "{chapter_title}"
- Key information and important turning points in the chapter are fully presented
- Sets the foundation for subsequent plot development

## 📝 Detailed Overview
This chapter primarily covers the core content of {chapter_title}. Through careful plot design and character development, the author successfully advances the story.

In this chapter, readers can see the growth and changes of main characters, as well as further deepening of the overall story plot. This not only enriches the layers of the story but also brings readers a more immersive reading experience.

## 👥 Key Characters
- **Main Character**: Plays a role in driving plot development in this chapter
- **Supporting Character**: Adds rich layers and depth to the story

## 🔍 Important Details
- The chapter contains key background information and plot elements
- The author showcases main scenes and situations through vivid descriptions
- This chapter provides important informational support for the entire story

## 💭 Chapter Significance
This chapter occupies an important position in the entire story, not only connecting previous plot points but also laying groundwork for future developments. Through this chapter, readers can better understand the story's core themes and character relationships.

## 🏷️ Keywords
`{chapter_title}` `plot development` `character building` `story progression` `thematic expression`

---
*Summary word count: ~350 words | Original word count: ~{word_count} words*
            """.strip()
        
        return mock_summary
    
    def generate_chapter_summary(self, chapter_title: str, chapter_content: str, language: str = 'zh-CN') -> Dict[str, Any]:
        """
        generate_chapter_summary 方法的别名，与views.py中的调用保持一致
        """
        return self.summarize_chapter(chapter_title, chapter_content, language)
