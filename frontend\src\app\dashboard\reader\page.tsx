'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { BookOpen, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function ReaderIndexPage() {
  const { user } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // 如果用户直接访问 /dashboard/reader/，重定向到书籍列表
    if (user) {
      router.push('/dashboard/books')
    }
  }, [user, router])

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Link
              href="/dashboard/books"
              className="flex items-center text-gray-500 hover:text-gray-700"
            >
              <ArrowLeft className="h-5 w-5 mr-1" />
              返回书籍列表
            </Link>
          </div>
        </div>
      </nav>
      
      <div className="flex items-center justify-center min-h-[calc(100vh-4rem)]">
        <div className="text-center">
          <BookOpen className="mx-auto h-16 w-16 text-gray-400 mb-4" />
          <h1 className="text-2xl font-semibold text-gray-900 mb-2">阅读器</h1>
          <p className="text-gray-500 mb-6">请选择一本书籍开始阅读</p>
          <Link
            href="/dashboard/books"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <BookOpen className="h-4 w-4 mr-2" />
            浏览书籍
          </Link>
        </div>
      </div>
    </div>
  )
}
