/* 章节列表可拖拽调整大小样式 */

/* 拖拽时的全局样式 */
.resizing {
  cursor: col-resize !important;
  user-select: none !important;
}

.resizing * {
  cursor: col-resize !important;
  user-select: none !important;
}

/* 侧边栏拖拽调整器样式 */
.sidebar-resizer {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  cursor: col-resize;
  z-index: 50;
  transition: all 0.2s ease;
}

.sidebar-resizer:hover {
  background: rgba(59, 130, 246, 0.2);
}

.sidebar-resizer.resizing {
  background: rgba(59, 130, 246, 0.4);
}

/* 拖拽指示器 */
.resize-indicator {
  position: absolute;
  right: -2px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 40px;
  background: linear-gradient(to right, rgba(156, 163, 175, 0.6), rgba(59, 130, 246, 0.6));
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.sidebar-resizer:hover .resize-indicator {
  opacity: 1;
  background: linear-gradient(to right, rgba(59, 130, 246, 0.8), rgba(37, 99, 235, 0.8));
}

.sidebar-resizer.resizing .resize-indicator {
  opacity: 1;
  background: linear-gradient(to right, rgba(37, 99, 235, 0.9), rgba(29, 78, 216, 0.9));
  transform: translateY(-50%) scale(1.1);
}

/* 宽度提示框 */
.width-tooltip {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(17, 24, 39, 0.95);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(55, 65, 81, 0.5);
  pointer-events: none;
  z-index: 60;
}

.width-tooltip::after {
  content: '';
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 6px solid transparent;
  border-left-color: rgba(17, 24, 39, 0.95);
}

/* 平滑过渡 */
.sidebar-transition {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.content-transition {
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-resizer {
    display: none;
  }
}
